from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "participant_activity"."activity_device" IS 'WITHINGS: withings
MANUAL_INPUT: manual_input
TRANSTEK: transtek';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "participant_activity"."activity_device" IS 'WITHINGS: withings
MANUAL_INPUT: manual_input';"""
