from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "uid_webinar_cover_u_a1521c";
        ALTER TABLE "webinar" DROP CONSTRAINT IF EXISTS "webinar_cover_url_key";
        """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE UNIQUE INDEX "uid_webinar_cover_u_a1521c" ON "webinar" ("cover_url");"""
