from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        --ALTER TABLE "live_session" DROP CONSTRAINT "fk_live_ses_cohort_137e8e9e";
        --ALTER TABLE "live_session" DROP CONSTRAINT "fk_live_ses_cohort_p_a278765d";
        CREATE TABLE "legacy_live_session" AS SELECT * FROM "live_session";
        UPDATE "participant_activity" SET live_session_id = NULL WHERE live_session_id IS NOT NULL;
        DELETE FROM "live_session";
        CREATE TABLE IF NOT EXISTS "webinar" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "title" VARCHAR(255) NOT NULL UNIQUE,
    "description" VARCHAR(500) NOT NULL UNIQUE,
    "topic" VARCHAR(1) NOT NULL,
    "recurrence" VARCHAR(1) NOT NULL,
    "duration" INT NOT NULL  DEFAULT 60,
    "max_capacity" INT,
    "cover_url" VARCHAR(255)  UNIQUE,
    "host_id" UUID REFERENCES "authorized" ("id") ON DELETE SET NULL
);
        CREATE TABLE IF NOT EXISTS "booking" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "status" VARCHAR(1) NOT NULL,
    "participant_id" UUID NOT NULL REFERENCES "participants" ("id") ON DELETE CASCADE,
    "webinar_id" UUID NOT NULL REFERENCES "webinar" ("id") ON DELETE CASCADE,
    CONSTRAINT "uid_booking_webinar_9f4391" UNIQUE ("webinar_id", "participant_id")
);
COMMENT ON COLUMN "booking"."status" IS 'BOOKED: 1\nATTENDED: 2\nCANCELED: 3';
        ALTER TABLE "live_session" ADD "webinar_id" UUID NOT NULL;
        ALTER TABLE "live_session" ADD "meeting_type" VARCHAR(1) NOT NULL  DEFAULT 8;
        ALTER TABLE "live_session" DROP COLUMN "cohort_id";
        ALTER TABLE "live_session" DROP COLUMN "cohort_program_module_id";
COMMENT ON COLUMN "live_session"."meeting_type" IS 'INSTANT: 1\nSCHEDULED: 2\nRECURRING_WITH_FIXED_TIME: 8';
COMMENT ON COLUMN "webinar"."topic" IS 'FOOD: 1\nEDUCATIONAL: 2\nACTIVITY: 3\nHEALTH_AND_WELLNESS: 4\nMENTAL_HEALTH: 5\nINTRO_SESSION: 6';
COMMENT ON COLUMN "webinar"."recurrence" IS 'MONTHLY: 1';
        ALTER TABLE "live_session" ADD CONSTRAINT "fk_live_ses_webinar_d3bab923" FOREIGN KEY ("webinar_id") REFERENCES "webinar" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" DROP CONSTRAINT "fk_live_ses_webinar_d3bab923";
        ALTER TABLE "live_session" ADD "cohort_id" UUID NOT NULL;
        ALTER TABLE "live_session" ADD "cohort_program_module_id" UUID NOT NULL;
        ALTER TABLE "live_session" DROP COLUMN "webinar_id";
        ALTER TABLE "live_session" DROP COLUMN "meeting_type";
        DROP TABLE IF EXISTS "booking";
        DROP TABLE IF EXISTS "webinar";
        ALTER TABLE "live_session" ADD CONSTRAINT "fk_live_ses_cohort_p_a278765d" FOREIGN KEY ("cohort_program_module_id") REFERENCES "cohort_program_modules" ("id") ON DELETE CASCADE;
        ALTER TABLE "live_session" ADD CONSTRAINT "fk_live_ses_cohort_137e8e9e" FOREIGN KEY ("cohort_id") REFERENCES "cohort" ("id") ON DELETE CASCADE;"""
