from typing import List, Optional
from uuid import UUID

from ciba_participant.common.aws_handler import create_folder_s3, delete_folder_s3
from ciba_participant.settings import ENV, get_settings

from .models import (
    FullProgram,
    FullProgramModule,
    Program,
    ProgramModule,
    ProgramModuleSection,
    RawProgramModule,
    RawProgramModuleSection,
)
from .pydantic_models import (
    ProgramCreate,
    ProgramModuleCreate,
    ProgramModuleOutput,
    ProgramModuleSectionCreate,
    ProgramModuleSectionOutput,
    ProgramModuleSectionUpdate,
    ProgramModuleUpdate,
    ProgramOutput,
    ProgramUpdate,
)

settings = get_settings()


class ProgramRepository:
    @staticmethod
    async def get_programs(page: int, per_page: int) -> list[FullProgram]:
        FullProgram.model_rebuild()

        programs = await Program.all().offset((page - 1) * per_page).limit(per_page)

        programs_output: list["FullProgram"] = []

        for program in programs:
            program_output = FullProgram(
                id=program.id,
                created_at=program.created_at,
                updated_at=program.updated_at,
                description=program.description,
                title=program.title,
                modules=None,
                cohorts=None,
            )

            programs_output.append(program_output)

        return programs_output

    @staticmethod
    async def get_all_program_ids() -> List[UUID]:
        programs = await Program.all().values("id")
        return [program["id"] for program in programs]

    @staticmethod
    async def get_all_programs_names_map() -> dict:
        programs = await Program.all().values("id", "title")
        return {program["id"]: program["title"] for program in programs}

    @staticmethod
    async def create_program(program: ProgramCreate) -> ProgramOutput:
        title_in_db = await Program.filter(title=program.title).exists()
        if title_in_db:
            raise ValueError(f"Program with title '{program.title}' already exists.")
        program_obj = await Program.create(**program.model_dump())
        if settings.ENV not in [ENV.TEST, ENV.LOCAL]:
            create_folder_s3(
                bucket_name=settings.AWS_BUCKET_NAME, folder_name=program_obj.title
            )
        return await ProgramOutput.from_orm(program_obj)

    @staticmethod
    async def get_program(program_id: UUID) -> Optional[FullProgram]:
        program = (
            await Program.filter(id=program_id)
            .prefetch_related("modules")
            .prefetch_related("modules__sections")
            .first()
        )

        if program is None:
            return None

        modules_output: list["FullProgramModule"] = []

        for module in program.modules:
            sections_output: list["RawProgramModuleSection"] = []

            for section in module.sections:
                assert isinstance(section.metadata, dict)

                sections_output.append(
                    RawProgramModuleSection(
                        id=section.id,
                        created_at=section.created_at,
                        updated_at=section.updated_at,
                        title=section.title,
                        description=section.description,
                        metadata=section.metadata,
                        program_module_id=section.program_module_id,
                        activity_type=section.activity_type,
                        activity_category=section.activity_category,
                    )
                )

            modules_output.append(
                FullProgramModule(
                    id=module.id,
                    created_at=module.created_at,
                    updated_at=module.updated_at,
                    title=module.title,
                    short_title=module.short_title,
                    length=module.length,
                    description=module.description,
                    program_id=module.program_id,
                    order=module.order,
                    sections=sections_output,
                    cohorts=None,
                )
            )

        program_output = FullProgram(
            id=program.id,
            created_at=program.created_at,
            updated_at=program.updated_at,
            description=program.description,
            title=program.title,
            modules=modules_output,
            cohorts=None,
        )

        return program_output

    @staticmethod
    async def get_programs_by_ids(program_ids: List[UUID]) -> List[ProgramOutput]:
        programs = await Program.filter(id__in=program_ids).prefetch_related(
            "modules__sections"
        )
        return [await ProgramOutput.from_orm(program) for program in programs]

    @staticmethod
    async def update_program(program_id: UUID, updates: ProgramUpdate) -> None:
        program = await Program.get(id=program_id)

        if updates.title and updates.title != program.title:
            if await Program.filter(id__not=program.id, title=updates.title).exists():
                raise ValueError(
                    f"Program with title '{updates.title}' already exists."
                )
            program.title = updates.title

        if updates.description and updates.description != program.description:
            program.description = updates.description

        await program.save()

    @staticmethod
    async def delete_program(program_id: UUID) -> None:
        program = await Program.get(id=program_id)
        if settings.ENV not in [ENV.TEST, ENV.LOCAL]:
            delete_folder_s3(
                bucket_name=settings.AWS_BUCKET_NAME, folder_name=program.title
            )
        await Program.filter(id=program_id).delete()


class ProgramModuleRepository:
    @staticmethod
    async def has_modules(program_id: UUID) -> bool:
        return await ProgramModule.filter(program_id=program_id).exists()

    @staticmethod
    async def get_program_modules_by_program(
        program_id: UUID,
    ) -> List[FullProgramModule]:
        modules = await ProgramModule.filter(program_id=program_id).all()

        modules_output: list["FullProgramModule"] = []

        for module in modules:
            modules_output.append(
                FullProgramModule(
                    id=module.id,
                    created_at=module.created_at,
                    updated_at=module.updated_at,
                    title=module.title,
                    short_title=module.short_title,
                    length=module.length,
                    description=module.description,
                    program_id=module.program_id,
                    order=module.order,
                )
            )

        return modules_output

    @staticmethod
    async def get_program_modules(
        page: int, per_page: int
    ) -> List[ProgramModuleOutput]:
        modules = (
            await ProgramModule.all().offset((page - 1) * per_page).limit(per_page)
        )
        return [await ProgramModuleOutput.from_orm(module) for module in modules]

    @staticmethod
    async def create_program_module(module: ProgramModuleCreate) -> ProgramModuleOutput:
        module_obj = await ProgramModule.create(**module.model_dump())
        return await ProgramModuleOutput.from_orm(module_obj)

    @staticmethod
    async def get_program_module(module_id: UUID) -> Optional[RawProgramModule]:
        module = await ProgramModule.get_or_none(id=module_id)

        if module is None:
            return None

        return RawProgramModule.model_validate(module)

    @staticmethod
    async def update_program_module(
        module_id: UUID, changes: ProgramModuleUpdate
    ) -> None:
        module = await ProgramModule.get_or_none(id=module_id)

        if not module:
            raise ValueError(f"Module with id {module_id} not found.")

        if changes.title and changes.title != module.title:
            module.title = changes.title

        if changes.short_title and changes.short_title != module.short_title:
            module.short_title = changes.short_title

        if changes.length and changes.length != module.length:
            # TODO: this will require live sessions to be update
            # module.length = changes.length
            raise NotImplementedError("Updating module length is not supported.")

        if changes.description and changes.description != module.description:
            module.description = changes.description

        if changes.program_id and changes.program_id != module.program_id:
            # TODO: this involves complex logic updates
            # module.program_id = changes.program_id
            raise NotImplementedError("Updating module program_id is not supported.")

        if changes.order and changes.order != module.order:
            module.order = changes.order

        await module.save()

    @staticmethod
    async def delete_program_module(module_id: UUID) -> None:
        await ProgramModule.filter(id=module_id).delete()

    @staticmethod
    async def get_program_module_sections(
        module_id: UUID,
    ) -> list[RawProgramModuleSection]:
        module = await ProgramModule.get(id=module_id)
        sections = await module.sections.all()

        sections_output: list["RawProgramModuleSection"] = []

        for section in sections:
            sections_output.append(RawProgramModuleSection.model_validate(section))

        return sections_output


class ProgramModuleSectionRepository:
    @staticmethod
    async def get_program_module_sections(
        page: int, per_page: int
    ) -> List[RawProgramModuleSection]:
        sections = (
            await ProgramModuleSection.all()
            .offset((page - 1) * per_page)
            .limit(per_page)
        )

        sections_output: list["RawProgramModuleSection"] = []

        for section in sections:
            sections_output.append(RawProgramModuleSection.model_validate(section))

        return sections_output

    @staticmethod
    async def create_program_module_section(
        section: ProgramModuleSectionCreate,
    ) -> RawProgramModuleSection:
        meta = {}

        if section.metadata:
            started_at = section.metadata.started_at

            if started_at and not isinstance(started_at, str):
                started_at = started_at.isoformat()

            meta = {
                "started_at": started_at,
                "url": section.metadata.url,
                "form_id": section.metadata.form_id,
                "type": section.metadata.type.value if section.metadata.type else None,
            }

        section_obj = await ProgramModuleSection.create(
            title=section.title,
            description=section.description,
            metadata=meta,
            program_module_id=section.program_module_id,
            activity_type=section.activity_type.value,
            activity_category=section.activity_category.value,
        )

        return RawProgramModuleSection.model_validate(section_obj)

    @staticmethod
    async def get_program_module_section(
        section_id: UUID,
    ) -> Optional[ProgramModuleSectionOutput]:
        section = await ProgramModuleSection.get_or_none(id=section_id)
        if section:
            return await ProgramModuleSectionOutput.from_orm(section)
        return None

    @staticmethod
    async def update_program_module_section(
        section_id: UUID, changes: ProgramModuleSectionUpdate
    ) -> None:
        section = await ProgramModuleSection.get_or_none(id=section_id)

        if section is None:
            raise ValueError(f"Section with id {section_id} not found.")

        if changes.title and changes.title != section.title:
            section.title = changes.title

        if changes.description and changes.description != section.description:
            section.description = changes.description

        if (
            changes.program_module_id
            and changes.program_module_id != section.program_module_id
        ):
            raise NotImplementedError("Updating program_module_id is not supported.")

        if changes.activity_type and changes.activity_type != section.activity_type:
            section.activity_type = changes.activity_type

        if (
            changes.activity_category
            and changes.activity_category != section.activity_category
        ):
            section.activity_category = changes.activity_category

        if changes.metadata:
            meta = changes.metadata.model_dump()
            meta["type"] = meta["type"].value if meta["type"] else None

            section.metadata = meta

        await section.save()

    @staticmethod
    async def delete_program_module_section(section_id: UUID) -> None:
        await ProgramModuleSection.filter(id=section_id).delete()
