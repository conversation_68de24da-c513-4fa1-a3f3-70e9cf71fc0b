from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from ciba_participant.activity.models import (
    ParticipantActivityCategory,
    ParticipantActivityEnum,
)
from ciba_participant.common.aws_handler import create_presigned_url_view
from ciba_participant.program.models import MetaType
from ciba_participant.settings import ENV, get_settings

settings = get_settings()


class ProgramBase(BaseModel):
    title: str
    description: Optional[str] = ""


class ProgramModuleBase(BaseModel):
    title: str
    short_title: str
    length: int
    description: str
    program_id: UUID
    order: int


class ProgramModuleSectionMetadata(BaseModel):
    started_at: Optional[datetime] = None
    url: Optional[str] = None
    form_id: Optional[str] = None
    type: Optional[MetaType] = None
    signed_url: Optional[str] = None


class ProgramModuleSectionBase(BaseModel):
    title: str
    description: str
    metadata: ProgramModuleSectionMetadata | None
    program_module_id: UUID
    activity_type: ParticipantActivityEnum
    activity_category: ParticipantActivityCategory


# Create models
class ProgramCreate(ProgramBase):
    pass


class ProgramModuleCreate(ProgramModuleBase):
    pass


class ProgramModuleSectionCreate(ProgramModuleSectionBase):
    pass


# Update models
class ProgramUpdate(ProgramBase):
    title: Optional[str] = None
    description: Optional[str] = None


class ProgramModuleUpdate(BaseModel):
    title: Optional[str] = None
    short_title: Optional[str] = None
    length: Optional[int] = None
    description: Optional[str] = None
    program_id: Optional[UUID] = None
    order: Optional[int] = None


class ProgramModuleSectionUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    metadata: Optional[ProgramModuleSectionMetadata] = None
    program_module_id: Optional[UUID] = None
    activity_type: Optional[ParticipantActivityEnum] = None
    activity_category: Optional[ParticipantActivityCategory] = None


def get_presigned_url(url: str | None) -> str | None:
    if not url:
        return None

    if not url.startswith("http"):
        if settings.ENV == ENV.LOCAL or settings.ENV == ENV.TEST:
            if url.startswith("/"):
                return f"http://localhost:8000{url}"
            return f"http://localhost:8000/{url}"

        return create_presigned_url_view(settings.AWS_BUCKET_NAME, url)

    return None


# Output models with nested relationships
class ProgramModuleSectionOutput(ProgramModuleSectionBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__
        # TODO: render signed_url
        metadata = obj_dict.get("metadata", None)

        if metadata:
            url = metadata.get("url", None)
            signed_url = metadata.get("signed_url", get_presigned_url(url))
            metadata["signed_url"] = signed_url

        return cls(**obj_dict)


class ProgramModuleOutput(ProgramModuleBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    sections: Optional[List[ProgramModuleSectionOutput]] = []
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    async def from_orm(cls, obj):
        sections = await obj.sections.all()
        obj_dict = obj.__dict__
        parsed_sections = []
        for section in sections:
            parsed_sections.append(await ProgramModuleSectionOutput.from_orm(section))
        obj_dict["sections"] = parsed_sections
        return cls(**obj_dict)


class ProgramOutput(ProgramBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    modules: Optional[List[ProgramModuleOutput]] = []

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    async def from_orm(cls, obj):
        modules = await obj.modules.all()
        obj_dict = obj.__dict__

        parsed_modules = []
        if modules:
            for module in modules:
                parsed_module = await ProgramModuleOutput.from_orm(module)
                parsed_modules.append(parsed_module)
        obj_dict["modules"] = parsed_modules
        return cls(**obj_dict)


class ProgramsOutput(BaseModel):
    programs: List[ProgramOutput]


class GetProgramsInput(BaseModel):
    page: int = Field(1, ge=1)
    per_page: int = Field(10, ge=1, le=100)
