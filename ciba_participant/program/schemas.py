from datetime import datetime
from uuid import UUID
from pydantic import BaseModel


class ProgramModuleInput(BaseModel):
    description: str
    program_id: UUID
    short_title: str
    title: str
    length: int


class ProgramModuleSectionInput(BaseModel):
    description: str
    metadata: dict
    program_module_id: UUID
    title: str
    activity_id: UUID


class CohortInput(BaseModel):
    limit: int
    started_at: datetime
    name: str
