from httpx import AsyncClient
from ciba_participant.settings import get_settings
from loguru import logger


settings = get_settings()


async def get_providers_ciba_api():
    """Return list of providers."""

    async with AsyncClient(
        base_url=settings.CIBA_API_HOST,
        headers={"X-Auth-Key": settings.CIBA_API_KEY},
        timeout=60,
    ) as client:
        try:
            response = await client.post(
                "/api/v1/providers",
            )
            if response.status_code == 200:
                return response.json()["data"]

        except Exception as ex:
            logger.error(f"Failed to get providers: {ex}")
            return []
    return []
