from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel
from tortoise.transactions import in_transaction

from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.content_library.enums import MaterialTag
from ciba_participant.content_library.helpers import (
    adjust_mime_type,
    sanitize_file_name,
)
from ciba_participant.content_library.models import (
    ContentMaterial,
    ContentTag,
    ContentActivityType,
    ProgramContent,
)
from ciba_participant.content_library.service import (
    check_permissions,
    check_duplicated_material_titles,
)


class NewMaterialData(BaseModel):
    title: str
    description: str
    mime_type: str
    content_url: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = 0
    file_location: Optional[str] = None
    form_id: Optional[str] = None
    activity_types: List[ParticipantActivityEnum]
    tags: List[MaterialTag]
    programs: List[UUID]


async def process(material_data: NewMaterialData, author_id: UUID) -> UUID:
    """
    Method that handles the creation of a new content material.
    :param material_data: The new content material data.
    :param author_id: ID of the author of the new content material.
    :return: ID of the new content material.
    """
    async with in_transaction():
        await check_permissions(author_id)

        if await check_duplicated_material_titles(material_data.title):
            raise ValueError(f"Duplicated material title: {material_data.title}")

        mime_type = (
            adjust_mime_type(material_data.content_url, material_data.form_id)
            if material_data.content_url or material_data.form_id
            else material_data.mime_type
        )

        new_material = await ContentMaterial.create(
            created_by=author_id,
            updated_by=author_id,
            mime_type=mime_type,
            file_name=sanitize_file_name(material_data.file_name),
            **material_data.model_dump(
                exclude={"tags", "activity_types", "programs", "mime_type", "file_name"}
            ),
        )

        tags_data = [
            ContentTag(tag=tag, material=new_material) for tag in material_data.tags
        ]

        await ContentTag.bulk_create(tags_data)

        activity_types = [
            ContentActivityType(
                activity_type=activity_type,
                material=new_material,
            )
            for activity_type in material_data.activity_types
        ]

        await ContentActivityType.bulk_create(activity_types)

        programs = [
            ProgramContent(
                program_id=program_id,
                material=new_material,
            )
            for program_id in material_data.programs
        ]

        await ProgramContent.bulk_create(programs)

        return new_material.id
