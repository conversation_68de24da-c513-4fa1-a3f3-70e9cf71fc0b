from uuid import UUID

from tortoise.transactions import in_transaction

from ciba_participant import get_settings
from ciba_participant.common.aws_handler import delete_s3_object
from ciba_participant.content_library.exceptions import ContentMaterialNotFoundError
from ciba_participant.content_library.models import ContentMaterial
from ciba_participant.content_library.service import check_permissions

settings = get_settings()


async def process(content_material_id: UUID, user_id: UUID) -> bool:
    """Delete content material."""
    async with in_transaction():
        await check_permissions(user_id)

        content_material = await ContentMaterial.filter(id=content_material_id).first()

        if content_material is None:
            raise ContentMaterialNotFoundError()

        if content_material.full_path:
            delete_s3_object(  # Would raise S3DeleteError if error happens
                bucket_name=settings.CONTENT_LIBRARY_BUCKET_NAME,
                object_path=content_material.full_path,
            )

        await content_material.delete()

        return True
