from uuid import UUID

from ciba_participant.content_library.enums import ContentMaterialStatus
from ciba_participant.content_library.exceptions import ContentMaterialNotFoundError
from ciba_participant.content_library.models import ContentMaterial
from ciba_participant.content_library.service import check_permissions


async def process(content_material_id: UUID, author_id: UUID):
    """
    Method that archive and un-archive the provided content material.
    :param content_material_id: ID of the content material to be archived or unarchived
    :param author_id: ID of the user that archived or unarchived the content material
    """
    await check_permissions(author_id)

    requested_material = await ContentMaterial.filter(id=content_material_id).first()

    if not requested_material:
        raise ContentMaterialNotFoundError()

    requested_material.status = get_new_status(requested_material)
    requested_material.updated_by = author_id
    await requested_material.save()


def get_new_status(content_material: ContentMaterial) -> ContentMaterialStatus:
    """
    Method that returns the new content material status based on the current status.
    :param content_material: Content material to be archived or unarchived
    :return: The new content material status
    """
    if content_material.status == ContentMaterialStatus.ARCHIVED:
        return ContentMaterialStatus.ACTIVE

    return ContentMaterialStatus.ARCHIVED
