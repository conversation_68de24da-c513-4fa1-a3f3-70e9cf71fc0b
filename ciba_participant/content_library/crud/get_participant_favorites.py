from typing import List
from uuid import UUID

from ciba_participant.content_library.models import ContentInteractions


async def process(participant_id: UUID) -> List[UUID]:
    """
    Method to get a participant favorite content material IDs.
    :param participant_id: ID of participant
    :return: A list with the IDs of the participant favorite content
    """
    return await ContentInteractions.filter(
        participant_id=participant_id, is_favorite=True
    ).values_list("material_id", flat=True)
