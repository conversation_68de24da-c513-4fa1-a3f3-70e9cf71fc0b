import base64

from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA1


def get_rsa_signature(message: str, private_key: str) -> bytes:
    """
    Method to generate an RSA signature using a private key.
    :param message: message to sign
    :param private_key: private key content
    :return: generated RSA signature
    """
    key = RSA.import_key(private_key.encode("utf-8"))
    # nosec (required by AWS CloudFront signed URL protocol)
    message_hash = SHA1.new(message.encode("utf-8"))
    signature = pkcs1_15.new(key).sign(message_hash)

    return signature


def make_signature_safe_for_cloudfront(signature: bytes) -> str:
    sanitized_signature = (
        base64.b64encode(signature)
        .replace(b"+", b"-")
        .replace(b"=", b"_")
        .replace(b"/", b"~")
    )

    return sanitized_signature.decode()


def sign_cloudfront_url(
    *, url: str, key_id: str, sign_key: str, expires_in: int
) -> str:
    policy = (
        f'{{"Statement":[{{"Resource":"{url}",'
        f'"Condition":{{"DateLessThan":{{"AWS:EpochTime":{expires_in}}}}}}}]}}'
    )
    signature = get_rsa_signature(policy, sign_key)
    safe_signature = make_signature_safe_for_cloudfront(signature)

    return f"{url}?Expires={expires_in}&Signature={safe_signature}&Key-Pair-Id={key_id}"
