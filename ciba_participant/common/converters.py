from datetime import datetime
from typing import Optional

import pendulum


def from_utc_to_pst(
    started_call: datetime, date_format: Optional[str] = None
) -> datetime | str:
    """Convert a UTC datetime to PST timezone, optionally format it."""
    if started_call is None:
        raise ValueError("Parameter 'started_call' cannot be None")
    # Assuming 'started_call' is naive (no timezone information),
    # explicitly set it to UTC or ensure it has UTC timezone
    if (
        started_call.tzinfo is None
        or started_call.tzinfo.utcoffset(started_call) is None
    ):
        started_call_utc = pendulum.instance(started_call, tz="UTC")
    else:
        started_call_utc = pendulum.instance(started_call)

    started_call_pst = started_call_utc.in_tz("America/Los_Angeles")

    if date_format:
        return started_call_pst.format(date_format)

    return started_call_pst
