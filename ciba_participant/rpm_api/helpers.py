from typing import Optional
from uuid import UUID

from ciba_participant.activity.models import ParticipantActivity
from ciba_participant.cohort.models import CohortMembers


async def get_sync_start_date(participant_id: UUID) -> Optional[int]:
    """
    Determines the start date for syncing participant data,
    based on the participant latest Withings measure or cohort start.

    Args:
        participant_id: The UUID of the participant.
    Returns:
        Unix timestamp representing the sync start date or None
        when no dates were found.
    """
    latest_measure = (
        await ParticipantActivity.filter(
            participant_id=participant_id, activity_device="withings"
        )
        .order_by("-created_at")
        .first()
    )

    if not latest_measure:
        cohort_membership = await CohortMembers.filter(
            participant_id=participant_id, status="active"
        ).first()

        if not cohort_membership:
            return None

        cohort = await cohort_membership.cohort

        return int(cohort.started_at.timestamp())

    return int(latest_measure.created_at.timestamp())
