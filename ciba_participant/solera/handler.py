from typing import Optional, Union
from uuid import uuid4

from fastapi import HTT<PERSON><PERSON>x<PERSON>
from starlette.status import HTTP_404_NOT_FOUND
from tortoise.transactions import in_transaction

from ciba_participant.chat_api.chat_api import assign_participant_to_chat
from ciba_participant.participant.user_service import UserService
from ciba_participant.participant.crud import ParticipantRepository
from ciba_participant.program.models import Program
from ciba_participant.solera.api import SoleraAPIAsyncClient
from ciba_participant.log.logging import logger
from ciba_participant.participant.models import (
    Participant,
    ParticipantStatus,
    SoleraParticipant,
)
from ciba_participant.cohort.models import <PERSON>hort, CohortMembers, CohortMembershipStatus
from ciba_participant.program.data_preparer import DataPreparer
from ciba_participant.settings import ENV, get_settings
from datetime import datetime, timedelta
from ciba_participant.common.aws_handler import (
    send_to_sqs,
    SQSNotification,
    NotificationType,
    EmailNotificationEvent,
)

settings = get_settings()


class RegistrationFailed:
    pass


class ParticipantDuplicatedError:
    pass


class SoleraHandler:
    """Service that keeps enroll flow logic for solera."""

    program_dict = {
        "NDPP": "NDPP",
        "IBC": "IBC",
        "HWM": "HWM",
    }

    def __init__(self) -> None:
        self.solera_company = "Solera"

    async def get_solera_user_info(self, solera_key: str) -> dict | HTTPException:
        response = await SoleraAPIAsyncClient(
            client_id=settings.SOLERA_CLIENT_ID,
            client_secret=settings.SOLERA_CLIENT_SECRET,
            environment=settings.ENV,
        ).get_user_info(solera_key)
        return response

    async def create_user_no_cohort(
        self, key: str
    ) -> Union[Participant, ParticipantDuplicatedError, RegistrationFailed]:
        """
        Create a user without assigning to a cohort.

        This method handles the following scenarios:
        1. New user registration
        2. Existing user in PENDING or ACTIVE status
        3. Re-enrollment of DELETED users with different careplan IDs

        Args:
            key: Solera key for user identification

        Returns:
            Participant: Successfully created or retrieved participant
            ParticipantDuplicatedError: If participant exists but cannot be enrolled
            RegistrationFailed: If registration process fails
        """
        # Get and validate Solera user information
        solera_user_info = await self._get_solera_user_info_safely(key)
        if not solera_user_info:
            return RegistrationFailed()

        email = solera_user_info.pop("email", "").lower()
        if not email:
            logger.error("Email not found in Solera user info.")
            return RegistrationFailed()

        # Prepare user data
        user_dict = self._prepare_user_dict(email, solera_user_info, key)
        logger.info(f"Solera user info: {solera_user_info}")
        logger.info(f"Program ID: {user_dict.get('solera_program_id')}")

        # Check if participant already exists
        participant = await self._get_existing_participant(email)
        if participant:
            return await self._handle_existing_participant(participant, user_dict, key)

        # Create new participant
        return await self._create_participant_with_cohort_check(user_dict)

    async def _handle_existing_participant(
        self, participant: Participant, user_dict: dict, key: str
    ) -> Union[Participant, ParticipantDuplicatedError, RegistrationFailed]:
        """
        Handle logic for existing participants based on their status.

        Args:
            participant: The existing participant
            user_dict: Dictionary with user data
            key: Solera key

        Returns:
            Participant, ParticipantDuplicatedError, or RegistrationFailed
        """
        # For active or pending participants, return the existing participant
        if participant.status in [ParticipantStatus.PENDING, ParticipantStatus.ACTIVE]:
            return participant

        # Handle deleted participants
        if participant.status == ParticipantStatus.DELETED:
            # Verify if the participant still has active cohort memberships and disenroll
            participant_cohorts = await CohortMembers.filter(
                participant_id=participant.id, status=CohortMembershipStatus.ACTIVE
            ).all()
            if participant_cohorts:
                logger.info(
                    f"Disenrolling participant {participant.id} from active cohorts."
                )
                for membership in participant_cohorts:
                    cohort = await membership.cohort
                    logger.info(
                        f"Removing participant {participant.id} from cohort {cohort.id}"
                    )
                    await ParticipantRepository.handle_cohort_removal(
                        participant, cohort
                    )

            # Get all solera_participant entries for this participant
            # A participant can have multiple solera_participant entries from previous enrollments
            solera_participants = await SoleraParticipant.filter(
                participant_id=participant.id
            ).all()

            # Check if any existing solera_participant has the same key
            # We don't allow re-enrollment with a careplan ID that was used before
            is_careplan_id_same = any(
                sp.solera_key == key for sp in solera_participants
            )

            # Only allow re-enrollment if the careplan ID is different from all existing ones
            if not is_careplan_id_same:
                # Check if a cohort is available for the program before re-enrolling
                cohort_exists = await self._has_available_cohort(
                    user_dict["solera_program_id"]
                )
                if not cohort_exists:
                    logger.error(
                        "No available cohort for the program. Cannot re-enroll user."
                    )
                    return RegistrationFailed()

                participant = await self._reenroll_user(user_dict)
                if not participant:
                    logger.error("Failed to reenroll user.")
                    return RegistrationFailed()
                return participant
            else:
                logger.error("Cannot re-enroll with the same careplan ID.")
                return RegistrationFailed()

        # For any other status
        logger.error(
            f"Participant status '{participant.status}' is not valid for enrollment."
        )
        return ParticipantDuplicatedError()

    async def _create_participant_with_cohort_check(
        self, user_dict: dict
    ) -> Union[Participant, RegistrationFailed]:
        """
        Create a new participant after checking for available cohort.

        Args:
            user_dict: Dictionary with user data

        Returns:
            Participant or RegistrationFailed
        """
        async with in_transaction():
            # Check if a cohort is available for the program
            cohort_exists = await self._has_available_cohort(
                user_dict["solera_program_id"]
            )
            if not cohort_exists:
                logger.error("No available cohort for the program.")

            # Create the new user
            participant = await self._create_new_user(user_dict)
            if not participant:
                logger.error("Failed to create new user.")
                return RegistrationFailed()

            return participant

    async def _get_solera_user_info_safely(self, key: str) -> Optional[dict]:
        try:
            solera_user_info = await self.get_solera_user_info(key)
            if isinstance(solera_user_info, dict):
                return solera_user_info
        except HTTPException as e:
            logger.exception(f"HTTPException while fetching Solera user info: {e}")
        except Exception as e:
            logger.exception(f"Exception while fetching Solera user info: {e}")
        return None

    async def _get_existing_participant(self, email: str) -> Optional[Participant]:
        """
        Get an existing participant by email.
        Includes deleted+{email} emails for compatibility with old disenroll.

        Args:
            email: The participant's email

        Returns:
            Participant or None
        """
        return await Participant.all_participants.filter(email__endswith=email).first()

    def _prepare_user_dict(self, email: str, solera_user_info: dict, key: str) -> dict:
        program_id = solera_user_info.pop("programId", "")
        if not program_id:
            logger.error("Program ID is missing from Solera user info.")
            raise ValueError("Program ID is required")

        user_dict = {
            "email": email,
            "first_name": solera_user_info.pop("given_name", ""),
            "last_name": solera_user_info.pop("family_name", ""),
            "group_id": uuid4(),
            "member_id": uuid4(),
            "medical_record": Participant.generate_medical_record(),
            "is_test": settings.ENV in [ENV.TEST, ENV.DEV, ENV.LOCAL],
            "solera_key": key,
            "solera_id": solera_user_info.pop("patientId", ""),
            "solera_program_id": program_id,
            "solera_enrollment_id": solera_user_info.get("enrollmentId", ""),
            "metadata": solera_user_info,
        }
        return user_dict

    async def _has_available_cohort(self, program_id: str) -> bool:
        tomorrow = datetime.now().date() + timedelta(days=1)
        return await Cohort.filter(
            started_at__gte=tomorrow, program__title=program_id
        ).exists()

    async def _create_new_user(self, user_dict: dict) -> Optional[Participant]:
        user_service = UserService()
        try:
            is_created = await user_service.create_user(user_dict)
            if is_created:
                return user_service.participant
            else:
                logger.error("UserService failed to create user.")
        except Exception as e:
            logger.exception(f"Exception while creating new user: {e}")
        return None

    async def _reenroll_user(self, user_dict: dict) -> Optional[Participant]:
        """
        Re-enroll a user who has been deleted.
        Args:
            user_dict: Dictionary with user data
        Returns:
            Participant or None
        """
        user_service = UserService()
        try:
            is_reenrolled = await user_service.re_enroll_user(user_dict)
            if is_reenrolled:
                return user_service.participant
            else:
                logger.error("UserService failed to reenroll user.")
        except Exception as e:
            logger.exception(f"Exception while reenroll user: {e}")
        return None

    async def create_user(
        self, key: str, correlation_id: str = "-"
    ) -> Optional[Participant]:
        solera_user_info = await self.get_solera_user_info(key)
        if isinstance(solera_user_info, dict):
            email = solera_user_info.pop("email").lower()

            # check if user exists
            exist = await Participant.filter(email=email).exists()
            if exist:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail="User already exists",
                )
            program_id = solera_user_info.pop("programId")
            user_dict = {
                "email": email,
                "first_name": solera_user_info.pop("given_name"),
                "last_name": solera_user_info.pop("family_name"),
                "group_id": str(uuid4()),
                "member_id": str(uuid4()),
                "medical_record": Participant.generate_medical_record(),
                "solera_key": key,
                "solera_id": solera_user_info.pop("patientId"),
                "solera_program_id": program_id,
                "solera_enrollment_id": solera_user_info["enrollmentId"],
                "metadata": solera_user_info,
            }
            logger.info(solera_user_info)
            logger.info(program_id)
            async with in_transaction():
                await self._check_program(program_id)
                user_service = UserService()
                await user_service.create_user(user_dict)

                participant = user_service.participant
                await self._add_program(participant, program_id)
                if participant is not None:
                    if settings.ENV == ENV.PROD:
                        notification = SQSNotification(
                            type=NotificationType.SQS,
                            email_event=EmailNotificationEvent.NEW_PARTICIPANT,
                            data={
                                "participant_id": participant.id,
                            },
                            correlation_id=correlation_id,
                        )

                        send_to_sqs(
                            queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                            message_body=notification.model_dump_json(),
                        )
                    await user_service.update_status(ParticipantStatus.ACTIVE)
                return participant
        return None

    async def _check_program(self, program_type: str) -> Program | None:
        program_type = program_type if program_type else self.program_dict["NDPP"]
        cohort_list = await DataPreparer().prepare_data(program_type)
        if not cohort_list:
            return Program()
        return None

    async def _add_program(
        self,
        participant: Optional[Participant] = None,
        program_type: Optional[str] = None,
    ) -> None:
        if participant is not None:
            if program_type is None:
                program_type = self.program_dict["NDPP"]
            program_group_list = await DataPreparer().prepare_data(program_type)
            if not program_group_list:
                raise HTTPException(
                    status_code=HTTP_404_NOT_FOUND,
                    detail="Program group not found",
                )
            if program_group_list[0]["id"]:
                cohort = (
                    await Cohort.filter(id=str(program_group_list[0]["id"]))
                    .prefetch_related("participants")
                    .get()
                )
                await cohort.participants.add(participant)
                # not needed for now
                if settings.ENV != ENV.LOCAL:
                    await assign_participant_to_chat(
                        cohort.unique_name,
                        participant.id,
                        participant.chat_identity,
                    )
