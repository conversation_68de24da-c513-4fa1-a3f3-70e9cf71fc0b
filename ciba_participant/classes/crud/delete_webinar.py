from uuid import UUID
from tortoise.transactions import in_transaction

from ciba_participant.classes.models import (
    Webinar,
)
from ciba_participant.error_messages.classes import (
    ERROR_CLASS_WITH_BOOKINGS,
)
from ciba_participant.classes.service import check_class_admin
from ciba_participant.schedule_manager.service import ScheduleManager
from ciba_participant.log.logging import logger


async def process(
    *,
    webinar_id: UUID,
    user_id: UUID,
):
    schedule_manager = ScheduleManager()

    async with in_transaction():
        await check_class_admin(user_id)

        webinar = await Webinar.get_or_none(id=webinar_id).prefetch_related(
            "sessions__bookings"
        )

        if webinar is None:
            raise Exception("Webinar not found")

        has_bookings = False
        meeting_ids = set()

        for session in webinar.sessions:
            if session.zoom_id is not None:
                meeting_ids.add(session.zoom_id)

            if len(session.bookings) > 0:
                has_bookings = True
                break

        if not has_bookings:
            for meeting_id in meeting_ids:
                try:
                    await schedule_manager.delete_zoom_meeting(meeting_id)
                except ValueError:
                    logger.warning(
                        f"The zoom meeting with ID {meeting_id} has been deleted already"
                    )

            await webinar.delete()
        else:
            raise Exception(ERROR_CLASS_WITH_BOOKINGS)
