from typing import List

from ciba_participant.classes.models import Webinar
from ciba_participant.participant.models import Authorized


async def get_webinar_creators() -> List[Authorized]:
    """
    Method to get all webinars creators.
    :return: Webinars creators list.
    """
    creators_id = await Webinar.all().distinct().values_list("host_id", flat=True)

    return await Authorized.filter(id__in=creators_id).all()
