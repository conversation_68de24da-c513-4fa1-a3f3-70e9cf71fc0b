from uuid import UUID

from tortoise.transactions import in_transaction

from ciba_participant.participant.models import Authorized
from ciba_participant.participant.crud import AuthorizedRepository
from ciba_participant.classes.crud.types import WebinarUpdate
from ciba_participant.classes.models import Webinar
from ciba_participant.classes.errors import WebinarError
from ciba_participant.classes.service import check_class_admin
from ciba_participant.schedule_manager.service import ScheduleManager
from ciba_participant.error_messages.classes import CLASS_NOT_FOUND


async def process(data: WebinarUpdate, user_id: UUID) -> UUID:
    schedule_manager = ScheduleManager()

    async with in_transaction():
        await check_class_admin(user_id)

        webinar = await Webinar.get_or_none(id=data.id).prefetch_related(
            "sessions", "host"
        )

        if webinar is None:
            raise WebinarError(CLASS_NOT_FOUND)

        must_change_host: bool = (
            webinar.host is not None
            and data.new_host is not None
            and (webinar.host.email.strip() != data.new_host.strip())
        ) or False

        must_change_title_or_description: bool = (
            data.title is not None and data.title.strip() != webinar.title
        ) or (
            data.description is not None
            and data.description.strip() != webinar.description
        )

        exclude = ["id", "new_host"]

        for k, v in vars(data).items():
            if v is None:
                exclude.append(k)

        update_data = data.model_dump(exclude=set(exclude))

        await webinar.update_from_dict(update_data)

        if must_change_title_or_description is True:
            for session in webinar.sessions:
                session.title = data.title or session.title
                session.description = data.description or session.description

                await session.save()

            for meeting_id in set([s.zoom_id for s in webinar.sessions]):
                await schedule_manager.update_title_and_description(
                    meeting_id, title=data.title, description=data.description
                )

        if data.new_host is not None and must_change_host is True:
            new_authorized = await Authorized.get_or_none(email=data.new_host)

            if new_authorized is not None:
                webinar.host_id = new_authorized.id

                alternative_hosts = [
                    alt_host.email
                    for alt_host in await AuthorizedRepository.get_alternative_hosts()
                    if alt_host.id != new_authorized.id
                ]

                for zoom_id in set([s.zoom_id for s in webinar.sessions]):
                    await schedule_manager.change_meeting_host(
                        zoom_id, data.new_host, alternative_hosts
                    )

        await webinar.save()

        return webinar.id
