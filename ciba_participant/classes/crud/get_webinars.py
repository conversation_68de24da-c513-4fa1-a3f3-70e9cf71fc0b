import math
from enum import StrEnum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel
from tortoise.queryset import QuerySet
from tortoise.fields import ReverseRelation

from ciba_participant.classes.models import (
    RawLiveSession,
    TopicEnum,
    TimeOfDayEnum,
    Webinar,
    FullWebinar,
    LiveSession,
    FullLiveSession,
    RawBooking,
    FullBooking,
    BookingStatusEnum,
)
from ciba_participant.classes.service import get_sessions_by_time_of_day
from ciba_participant.participant.models import RawAuthorized, RawParticipant


class Include(StrEnum):
    participants = "sessions__bookings__participant"
    bookings = "sessions__bookings"
    sessions = "sessions"
    host = "host"


class FilterInput(BaseModel):
    title_like: Optional[str] = None
    webinar_host: Optional[UUID] = None
    topic: Optional[TopicEnum] = None
    time_of_day: Optional[TimeOfDayEnum] = None


class GetWebinarsOutput(BaseModel):
    webinars: list[FullWebinar]
    total_pages: int


async def add_filters(
    query: QuerySet[Webinar],
    filters: FilterInput,
    timezone_str: str,
) -> QuerySet:
    """
    Adds filters to a given query based on specified filter input fields.

    This function updates a QuerySet by applying filtering conditions based on
    the provided FilterInput instance. It checks each filter field in the
    FilterInput object and applies a corresponding filter to the QuerySet. If
    a filter field is not provided or is None, the query will remain unaltered
    for that field.

    Arguments:
        query: QuerySet
            The QuerySet instance to which filters are applied. This QuerySet
            represents a collection of Webinar objects.
        filters: FilterInput
            An instance of a filter input containing criteria for narrowing
            down the QuerySet. Each field in the FilterInput object specifies
            a certain aspect of the filtering process (e.g., title_like,
            webinar_host, time_of_day, and topic).
        timezone_str: str
            The timezone string to use for time-related operations.

    Returns:
        QuerySet:
            The updated QuerySet containing webinars that match the given
            filters.
    """
    if filters.title_like:
        query = query.filter(title__icontains=filters.title_like)

    if filters.webinar_host:
        query = query.filter(host_id=filters.webinar_host)

    if filters.time_of_day:
        sessions = await get_sessions_by_time_of_day(
            timezone_str=timezone_str, time_of_day=filters.time_of_day
        )
        query = query.filter(sessions__in=sessions).distinct()

    if filters.topic:
        query = query.filter(topic=filters.topic)

    return query


def process_sessions(
    sessions: ReverseRelation[LiveSession], include: set[Include]
) -> list[FullLiveSession]:
    """
    Process webinar sessions and compile session data for further usage.

    This function iterates through the sessions of a given webinar object,
    processes raw session data into validated models, and optionally includes
    detailed bookings and participant information based on the input criteria.

    To use this function, ensure that sessions are prefetched for
    Parameters:
        sessions (object): The sessions data to process.
        include (set[Include]): A set of `Include` enum values specifying
        the level of data detail to include, such as bookings or participants.

    Returns:
        list[FullLiveSession]: A list of processed session data, including optional
        bookings and participant details based on the `include` parameter.
    """
    output = []
    for session in sessions:
        live_session = RawLiveSession.model_validate(session)

        bookings_output: Optional[list[FullBooking]] = []
        if Include.bookings in include or Include.participants in include:
            live_session.bookings_count = len(
                [
                    booking
                    for booking in session.bookings
                    if booking.status != BookingStatusEnum.CANCELED
                ]
            )

            for booking in session.bookings:
                raw_booking_output = RawBooking.model_validate(booking)

                participant_output: Optional[RawParticipant] = None
                if Include.participants in include:
                    participant_output = RawParticipant.model_validate(
                        booking.participant
                    )
                bookings_output.append(
                    FullBooking(
                        **raw_booking_output.model_dump(),
                        participant=participant_output,
                    )
                )

        output.append(
            FullLiveSession(
                **live_session.model_dump(),
                bookings=bookings_output,
            )
        )

    return output


async def process(
    *,
    page: int,
    per_page: int,
    include: set[Include],
    filters: Optional[FilterInput] = None,
    timezone_str: str = "UTC",
) -> GetWebinarsOutput:
    """
    Process webinars to fetch data including filters, inclusions, and pagination.

    This function retrieves, filters, and paginates webinar data based on the provided
    parameters. Optionally, it processes additional details related to the webinars
    (e.g., sessions, host) depending on the `include` parameter. The output contains a list
    of webinars and the total number of pages.

    Arguments:
        page (int): The current page number for pagination. Must be a positive integer.
        per_page (int): The number of webinars to retrieve per page. Must be a positive integer.
        include (set[Include]): A set of inclusion markers specifying which additional
            related entities (e.g., sessions, host) should be included in the output.
        filters (Optional[FilterInput], optional): Optional filter criteria to restrict the
            webinar data. Defaults to None.
        timezone_str (str, optional): The timezone string to use for time-related operations.

    Returns:
        GetWebinarsOutput: An object containing the list of webinars and the total number
            of pages based on the provided parameters.

    Raises:
        None: The function does not explicitly raise specific exceptions. Refer to inner
            function implementations and library methods for potential exceptions.
    """
    offset = (page - 1) * per_page

    query = Webinar.all()

    if include:
        query = query.prefetch_related(*include)

    if filters:
        query = await add_filters(
            query=query, filters=filters, timezone_str=timezone_str
        )

    webinar_ids = await query.distinct().group_by("id").values_list("id", flat=True)

    total_webinars = len(webinar_ids)
    total_pages = math.ceil(total_webinars / per_page)

    webinars = await query.offset(offset).limit(per_page).order_by("-created_at")

    webinars_output: list[FullWebinar] = []

    for webinar in webinars:
        live_sessions_output: list[FullLiveSession] = []

        if Include.sessions in include:
            live_sessions_output = process_sessions(
                sessions=webinar.sessions, include=include
            )

        host_output: Optional[RawAuthorized] = None

        if Include.host in include and webinar.host is not None:
            host_output = RawAuthorized.model_validate(webinar.host)

        webinars_output.append(
            FullWebinar(
                id=webinar.id,
                created_at=webinar.created_at,
                updated_at=webinar.updated_at,
                title=webinar.title,
                description=webinar.description,
                topic=webinar.topic,
                recurrence=webinar.recurrence,
                cover_url=webinar.cover_url,
                duration=webinar.duration,
                max_capacity=webinar.max_capacity,
                sessions=live_sessions_output,
                host=host_output,
            )
        )

    return GetWebinarsOutput(webinars=webinars_output, total_pages=total_pages)
