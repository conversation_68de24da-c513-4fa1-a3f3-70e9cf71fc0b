from datetime import datetime
from typing import Optional, List

from asyncpg.pgproto.pgproto import timedelta
from tortoise.queryset import RawSQL, Q

from ciba_participant.classes.crud.get_webinars import FilterInput
from ciba_participant.classes.models import (
    TimeOfDayEnum,
    LiveSession,
    RawLiveSession,
    BookingStatusEnum,
)
from ciba_participant.common.validations.dates import (
    validate_date_range,
    validate_date_range_duration,
)


def get_query_annotations(filters: Optional[FilterInput], timezone: str):
    annotations = {
        "parsed_date": RawSQL(f"meeting_start_time AT TIME ZONE '{timezone}'")
    }

    if not filters:
        return annotations

    if filters.time_of_day:
        annotations["parsed_hour"] = RawSQL(
            f"EXTRACT(HOUR FROM meeting_start_time AT TIME ZONE '{timezone}')"
        )

    return annotations


def get_query_filters(
    start_date: datetime, end_date: datetime, filters: Optional[FilterInput]
):
    actual_filters = [
        Q(meeting_start_time__gte=start_date),
        Q(meeting_start_time__lte=end_date),
    ]

    if not filters:
        return actual_filters

    if filters.title_like:
        actual_filters.append(Q(title__icontains=filters.title_like))
    if filters.time_of_day and filters.time_of_day == TimeOfDayEnum.MORNING:
        actual_filters.append(Q(parsed_hour__lt=12))
    if filters.time_of_day and filters.time_of_day == TimeOfDayEnum.AFTERNOON:
        actual_filters.append(Q(Q(parsed_hour__gte=12) & Q(parsed_hour__lt=18)))
    if filters.time_of_day and filters.time_of_day == TimeOfDayEnum.EVENING:
        actual_filters.append(Q(parsed_hour__gte=18))
    if filters.topic:
        actual_filters.append(Q(webinar__topic=filters.topic))
    if filters.webinar_host:
        actual_filters.append(Q(webinar__host_id=filters.webinar_host))

    return actual_filters


async def parse_session(model: LiveSession) -> RawLiveSession:
    """
    Method to map a live session model to a raw live session object.
    :param model: live session database model
    :return: serialized raw live session object
    """
    session = RawLiveSession.model_validate(model)

    session.bookings_count = await model.bookings.filter(
        status__not=BookingStatusEnum.CANCELED
    ).count()
    session.host_id = model.webinar.host_id
    session.topic = model.webinar.topic
    session.max_capacity = model.webinar.max_capacity

    return session


async def get_live_sessions(
    *,
    start_date: datetime,
    end_date: datetime,
    timezone: str = "UTC",
    filters: Optional[FilterInput] = None,
) -> List[RawLiveSession]:
    """
    Method to retrieve live sessions with the given filters.
    :param start_date: start date to search sessions
    :param end_date: end date to search sessions
    :param timezone: time zone string
    :param filters: filters object to be applied on the query
    :return: list of live sessions
    """
    validate_date_range(start_date, end_date)
    validate_date_range_duration(start_date, end_date, timedelta(days=46))

    annotations = get_query_annotations(filters, timezone)
    filters = get_query_filters(start_date, end_date, filters)

    sessions = (
        await LiveSession.annotate(**annotations)
        .filter(*filters)
        .prefetch_related("webinar")
        .order_by("meeting_start_time")
        .all()
    )

    return [await parse_session(session) for session in sessions]
