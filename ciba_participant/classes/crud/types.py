from typing import Optional
from datetime import datetime
from uuid import UUID

from pydantic import BaseModel

from ciba_participant.classes.models import (
    RecurrenceEnum,
    TopicEnum,
)


class WebinarCreate(BaseModel):
    topic: TopicEnum
    title: str
    description: str
    max_capacity: int = 250
    timezone: str
    meeting_start_time: datetime
    number_of_sessions: int
    recurrence: RecurrenceEnum
    host_id: UUID
    duration: Optional[int] = 60
    cover_url: Optional[str] = None


class WebinarUpdate(BaseModel):
    id: UUID
    topic: Optional[TopicEnum] = None
    max_capacity: Optional[int] = None
    meeting_start_time: Optional[datetime] = None
    title: Optional[str] = None
    description: Optional[str] = None
    duration: Optional[int] = None
    cover_url: Optional[str] = None
    new_host: Optional[str] = None
