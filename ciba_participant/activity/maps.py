from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    SoleraParticipantActivityEnum,
)


PARTICIPANT_TO_SOLERA_MAP = {
    ParticipantActivityEnum.ENROLL: SoleraParticipantActivityEnum.ENROLL,
    ParticipantActivityEnum.WEIGHT: SoleraParticipantActivityEnum.WEIGHT,
    ParticipantActivityEnum.PLAY: SoleraParticipantActivityEnum.PLAY,
    ParticipantActivityEnum.COACH: SoleraParticipantActivityEnum.COACH,
    ParticipantActivityEnum.RECIPES: SoleraParticipantActivityEnum.RECIPES,
    ParticipantActivityEnum.QUIZ: SoleraParticipantActivityEnum.QUIZ,
    ParticipantActivityEnum.ACTIVITY: SoleraParticipantActivityEnum.ACTIVITY,
    ParticipantActivityEnum.ARTICLE: SoleraParticipantActivityEnum.ARTICLE,
    ParticipantActivityEnum.GROUP: SoleraParticipantActivityEnum.GROUP,
}

SOLERA_TO_PARTICIPANT_MAP = {v: k for k, v in PARTICIPANT_TO_SOLERA_MAP.items()}
