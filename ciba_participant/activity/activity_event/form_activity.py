from typing import NotRequired, TypedDict
from uuid import uuid4

from pydantic import UUID4

from .activity_event import ActivityEvent


class FormFilledPayload(TypedDict):
    response_id: NotRequired[str]


class FormFilledEvent(ActivityEvent[FormFilledPayload | None]):
    name: str = "form_filled"
    activity_type: str = "form_activity"


def create_form_filled_event(
    *,
    participant_id: UUID4,
    activity_id: UUID4 | None = None,
    payload: FormFilledPayload | None = None,
) -> FormFilledEvent:
    return FormFilledEvent(
        participant_id=participant_id,
        activity_id=activity_id or uuid4(),
        payload=payload,
    )
