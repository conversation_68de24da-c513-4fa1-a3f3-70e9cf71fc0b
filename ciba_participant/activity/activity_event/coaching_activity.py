from uuid import uuid4

from pydantic import UUID4

from .activity_event import ActivityEvent


class PhysicalActivityDoneEvent(ActivityEvent[None]):
    name: str = "coach_called"
    activity_type: str = "coaching_activity"


def create_physical_activity_done_event(
    *,
    participant_id: UUID4,
    activity_id: UUID4 | None = None,
) -> PhysicalActivityDoneEvent:
    return PhysicalActivityDoneEvent(
        participant_id=participant_id,
        activity_id=activity_id or uuid4(),
        payload=None,
    )
