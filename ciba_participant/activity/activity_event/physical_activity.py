from typing import TypedDict
from uuid import uuid4

from pydantic import UUID4

from .activity_event import ActivityEvent


class PhysicalActivityDonePayload(TypedDict):
    # In seconds
    duration: int


class PhysicalActivityDoneEvent(ActivityEvent[PhysicalActivityDonePayload]):
    name: str = "physical_activity_done"
    activity_type: str = "physical_activity"


def create_physical_activity_done_event(
    *,
    participant_id: UUID4,
    activity_id: UUID4 | None = None,
    payload: PhysicalActivityDonePayload,
) -> PhysicalActivityDoneEvent:
    return PhysicalActivityDoneEvent(
        participant_id=participant_id,
        activity_id=activity_id or uuid4(),
        payload=payload,
    )
