# from __future__ import annotations
#
# from datetime import datetime
# from enum import StrEnum
# from typing import Any, Generic, TypeVar
# from uuid import uuid4
#
# from psycopg.rows import class_row
# from psycopg.types.json import Jsonb
# from pydantic import UUID4, BaseModel, Field
# from pypika import Column, Query, Table
# from pypika.enums import ReferenceOption
# from pypika.terms import Criterion, FormatParameter, Function, LiteralValue
#
# from ciba_participant.common import ConnectionManager
# from ciba_participant.utils import now_in_utc
#
# T = TypeVar("T")
#
# ACTIVITY_EVENT_TABLE = "activity_event"
#
#
# class ActivityEvent(BaseModel, Generic[T]):
#     id: UUID4 = Field(default_factory=uuid4)
#     name: str
#     timestamp: datetime = Field(default_factory=now_in_utc)
#     activity_id: UUID4 = Field(default_factory=uuid4)
#     activity_type: str
#     participant_id: UUID4
#     payload: T
#
#     @staticmethod
#     async def prepare_db(
#         *,
#         use_participant_fk: bool = False,
#         participant_table: str | None = None,
#         participant_reference_column: str | None = "id",
#     ):
#         create_table_query = (
#             Query.create_table(ACTIVITY_EVENT_TABLE)
#             .if_not_exists()
#             .columns(
#                 Column(
#                     "id",
#                     "UUID",
#                     nullable=False,
#                     default=Function("gen_random_uuid"),
#                 ),
#                 Column("name", "VARCHAR(150)", nullable=False),
#                 Column(
#                     "timestamp",
#                     "TIMESTAMP",
#                     nullable=False,
#                     default=LiteralValue("(now() AT TIME ZONE 'utc')"),
#                 ),
#                 Column(
#                     "activity_id",
#                     "UUID",
#                     nullable=False,
#                     default=Function("gen_random_uuid"),
#                 ),
#                 Column("activity_type", "VARCHAR(150)", nullable=False),
#                 Column("participant_id", "UUID", nullable=False),
#                 Column("payload", "JSON", nullable=True),
#             )
#             .primary_key("id")
#             .unique(Column("participant_id"))
#         )
#
#         if use_participant_fk:
#             if not participant_table:
#                 raise ValueError("`participant_table` missing")
#             if not participant_reference_column:
#                 raise ValueError("`participant_reference_column` missing")
#
#             create_table_query = create_table_query.foreign_key(
#                 columns=[Column("participant_id")],
#                 reference_table=Table(participant_table),
#                 reference_columns=[Column(participant_reference_column)],
#                 on_delete=ReferenceOption.cascade,
#             )
#
#         conn = ConnectionManager.connection()
#
#         async with conn.transaction():
#             await conn.execute(str(create_table_query))
#
#             for column, index_name in ActivityEventIndexes.__members__.items():
#                 index_query = (
#                     Query.create_index(index_name)
#                     .on(ACTIVITY_EVENT_TABLE)
#                     .columns(Column(column))
#                 )
#
#                 await conn.execute(str(index_query))
#
#     @staticmethod
#     async def filter(
#         *,
#         id: UUID4 | None = None,
#         name: str | None = None,
#         activity_id: UUID4 | None = None,
#         activity_name: str | None = None,
#         participant_id: UUID4 | None = None,
#         custom: Criterion | None = None,
#         row_factory: type[ActivityEvent] | None = None,
#     ):
#         row_factory = row_factory or ActivityEvent
#         ae = Table(ACTIVITY_EVENT_TABLE)
#
#         query = Query.from_(ae).select("*")
#
#         if id is not None:
#             query = query.where(ae.id == id)
#
#         if name is not None:
#             query = query.where(ae.name == name)
#
#         if activity_id is not None:
#             query = query.where(ae.activity_id == activity_id)
#
#         if activity_name is not None:
#             query = query.where(ae.activity_name == activity_name)
#
#         if participant_id is not None:
#             query = query.where(ae.participant_id == participant_id)
#
#         if custom is not None:
#             query = query.where(custom)
#
#         conn = ConnectionManager.connection()
#
#         cur = conn.cursor(row_factory=class_row(row_factory))
#         cur = await cur.execute(str(query))
#
#         return await cur.fetchall()
#
#     @staticmethod
#     async def save(*events: ActivityEvent):
#         ae = Table(ACTIVITY_EVENT_TABLE)
#
#         query = Query.into(ae)
#         all_values = []
#
#         for event in events:
#             values = [
#                 event.id,
#                 event.name,
#                 event.timestamp,
#                 event.activity_id,
#                 event.activity_type,
#                 event.participant_id,
#                 Jsonb(event.payload),
#             ]
#
#             all_values += values
#             query = query.insert(*(FormatParameter() for _ in values))
#
#         conn = ConnectionManager.connection()
#
#         await conn.execute(str(query), all_values)
#
#
# class ActivityEventIndexes(StrEnum):
#     name = "name_idx"
#     timestamp = "timestamp_idx"
#     activity_id = "activity_id_idx"
#     activity_type = "activity_type_idx"
#
#
# def create_activity_event(
#     *,
#     event_name: str,
#     participant_id: UUID4,
#     activity_type: str,
#     payload: Any,
#     activity_id: UUID4 | None = None,
# ) -> ActivityEvent[Any]:
#     return ActivityEvent(
#         name=event_name,
#         participant_id=participant_id,
#         activity_id=activity_id or uuid4(),
#         activity_type=activity_type,
#         payload=payload,
#     )
