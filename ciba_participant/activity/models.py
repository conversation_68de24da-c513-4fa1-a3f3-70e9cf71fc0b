from enum import Enum
from typing import TYPE_CHECKING
from uuid import UUID, uuid4

from tortoise.fields import (
    BooleanField,
    CharEnumField,
    CharField,
    DatetimeField,
    ForeignKeyField,
    NO_ACTION,
    ForeignKeyNullableRelation,
    UUIDField,
)
from tortoise.models import Model

if TYPE_CHECKING:
    from ciba_participant.program.models import ProgramModuleSection
    from ciba_participant.classes.models import LiveSession


class ParticipantActivityEnum(Enum):
    """Enum for participant activities."""

    ENROLL = "enrollment_type"
    WEIGHT = "weight_type"
    PLAY = "video_type"
    COACH = "chat_type"
    RECIPES = "recipe_type"
    QUIZ = "personal_success"
    ACTIVITY = "activity_type"
    ARTICLE = "curriculum"
    GROUP = "coaching_call"


class SoleraParticipantActivityEnum(Enum):
    """Enum for participant activities."""

    ENROLL = "enrollment"
    WEIGHT = "weight"
    PLAY = "videoWatched"
    COACH = "coachInteraction"
    MEALS = "mealsLogged"
    RECIPES = "recipeEducation"
    QUIZ = "quizCompleted"
    ACTIVITY = "physicalActivity"
    ARTICLE = "articleRead"
    GROUP = "groupInteraction"


class ParticipantActivityCategory(Enum):
    SYSTOLIC = "systolic"
    DIASTOLIC = "diastolic"
    HEART_RATE = "heart_rate"
    WEIGHT = "weight"
    BMI = "bmi"
    ACTIVITY = "activity"
    STEPS = "steps"


class ParticipantActivityDevice(Enum):
    """Type of device to track activity"""

    WITHINGS = "withings"
    MANUAL_INPUT = "manual_input"
    TRANSTEK = "transtek"


class ActivityUnit(Enum):
    """Activity unit"""

    ACTION = "action"
    KG = "kg"
    LB = "lbs"


class TrendEnum(Enum):
    """Trend enum"""

    UP = "up"
    DOWN = "down"
    FLAT = "flat"


class ParticipantActivity(Model):
    """Participant activities"""

    id = UUIDField(primary_key=True, default=uuid4)
    participant_id: UUID
    participant = ForeignKeyField(
        "models.Participant",
        related_name="activities",
    )
    value = CharField(max_length=255, null=True)
    unit = CharEnumField(enum_type=ActivityUnit)
    activity_device = CharEnumField(enum_type=ParticipantActivityDevice)
    activity_category = CharEnumField(enum_type=ParticipantActivityCategory)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(null=True, auto_now=True)
    activity_type = CharEnumField(enum_type=ParticipantActivityEnum)
    section_id: UUID | None
    section: ForeignKeyNullableRelation["ProgramModuleSection"] = ForeignKeyField(
        "models.ProgramModuleSection",
        related_name="activities",
        null=True,
    )
    live_session_id: UUID | None
    live_session: ForeignKeyNullableRelation["LiveSession"] = ForeignKeyField(
        "models.LiveSession",
        related_name="activities",
        on_delete=NO_ACTION,
        null=True,
    )
    is_content_related = BooleanField(default=False)

    class Meta:
        table = "participant_activity"
        unique_together = ("id", "created_at")
