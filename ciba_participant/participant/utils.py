import secrets
import string


class CouldNotFindValidPassword(Exception):
    """Exception for cases when valid password could not be generated."""


def generate_random_password(pwd_length: int = 12) -> str:
    """Generate random password.

    Random password should contain at least one uppercase letter,
    lowercase letter, digit and special char.
    """
    ascii_lowercase = string.ascii_lowercase
    ascii_uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = string.punctuation
    alphabet = ascii_lowercase + ascii_uppercase + digits + special_chars
    # give it only 100 attempts to find valid password
    for _ in range(100):
        pwd = ""
        for _ in range(pwd_length):
            pwd += "".join(secrets.choice(alphabet))

        if (
            any(char in ascii_lowercase for char in pwd)
            and any(char in ascii_uppercase for char in pwd)
            and any(char in digits for char in pwd)
            and any(char in special_chars for char in pwd)
        ):
            break
    else:
        raise CouldNotFindValidPassword
    return pwd
