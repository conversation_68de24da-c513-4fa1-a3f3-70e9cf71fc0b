from ciba_participant.classes.models import Booking, TopicEnum
from ciba_participant.cohort.models import <PERSON>hor<PERSON>
from ciba_participant.log.logging import logger
from ciba_participant.participant.models import Participant
from ciba_participant.ciba_api.api import get_providers_ciba_api
from ciba_participant.participant.models import Authorized, RawAuthorized


class DataPreparer:
    """Prepare data for send coaching call notification"""

    def __init__(self, participant_id: str) -> None:
        self.participant_id = participant_id

    async def prepare_data(self) -> dict | None:
        return await self._prepare_by_participant()

    async def _prepare_by_participant(self) -> dict | None:
        """Prepare data by participant"""
        if self.participant_id:
            logger.debug(f"Prepare data by participant: {self.participant_id}")
            participant = (
                await Participant.filter(id=self.participant_id)
                .prefetch_related("solera_participant")
                .first()
            )
            if not participant:
                logger.info(
                    f"No participant with id: {self.participant_id} in database"
                )
                return None
            cohort = (
                await Cohort.filter(participants__id=participant.id)
                .order_by("-started_at")
                .first()
            )
            first_call = (
                await Booking.filter(
                    participant_id=participant.id,
                    live_session__webinar__topic=TopicEnum.INTRO_SESSION,
                )
                .prefetch_related("live_session")
                .first()
            )
            return {
                "id": cohort.id,
                "started_call": first_call.live_session.meeting_start_time,
                "started_program": cohort.started_at,
                "email": participant.email,
                "first_name": participant.first_name,
                "last_name": participant.last_name,
                "program_name": participant.solera_participant[0].solera_program_id,
            }
        else:
            return None


async def extend_provider_data(filters=None) -> list:
    """Extend provider data with participant data"""
    provider_data = []
    try:
        query = Authorized.all()

        if filters and filters.role:
            query = query.filter(role=filters.role.value)

        authorized_users = await query
        ciba_api_providers = await get_providers_ciba_api()

        ciba_providers_email_map = {
            provider["email"]: provider for provider in ciba_api_providers
        }

        for authorized in authorized_users:
            participant_admin = RawAuthorized.parse_obj(authorized).model_dump()
            if authorized.email in ciba_providers_email_map:
                ciba = ciba_providers_email_map[authorized.email]
                participant_admin["old_chat_identity"] = (
                    ciba["chat_identity"] if ciba else None
                )
            else:
                participant_admin["old_chat_identity"] = None
            provider_data.append(participant_admin)
    except Exception as e:
        logger.error(f"Failed to get authorized users: {e}")
    return provider_data
