from pydantic import BaseModel, EmailStr, <PERSON>
from typing import Optional
from uuid import UUID
from datetime import datetime


class ParticipantInput(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    group_id: str
    member_id: str
    status: str = Field(default="pending")
    cognito_sub: Optional[UUID]
    medical_record: Optional[str]
    is_test: bool = Field(default=False)
    last_reset: Optional[datetime]


class SoleraParticipantInput(BaseModel):
    participant_id: UUID
    solera_id: Optional[UUID]
    solera_key: str
    solera_program_id: str
    solera_enrollment_id: str


class HeadsUpParticipantInput(BaseModel):
    participant_id: UUID
    heads_up_token: Optional[str]
    heads_up_id: Optional[str]


class ParticipantMetaInput(BaseModel):
    participant_id: UUID
    metadata: Optional[dict]
