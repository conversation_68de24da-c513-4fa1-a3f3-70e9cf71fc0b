import json
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, field_validator

from ciba_participant.activity.pydantic_models import ParticipantActivityOutput
from ciba_participant.classes.models import (
    RawLiveSession,
    RawBooking,
    RawWebinar,
)
from ciba_participant.participant.models import (
    AutorizedRole,
    ParticipantStatus,
    RawParticipant,
    RawAuthorized,
)


class AuthorizedBase(BaseModel):
    email: str
    first_name: str
    last_name: str
    role: AutorizedRole


class AuthorizedCreate(AuthorizedBase):
    status: ParticipantStatus = ParticipantStatus.PENDING


class AuthorizedUpdate(AuthorizedBase):
    id: UUID
    email: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    role: Optional[AutorizedRole]
    status: Optional[ParticipantStatus]


class AuthorizedPydantic(AuthorizedBase):
    id: UUID
    chat_identity: str
    created_at: datetime
    updated_at: datetime
    status: ParticipantStatus

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__
        obj_dict["chat_identity"] = obj.chat_identity
        return cls(**obj_dict)


class ParticipantBase(BaseModel):
    email: str
    first_name: str
    last_name: str
    group_id: UUID
    member_id: UUID
    status: ParticipantStatus
    cognito_sub: Optional[UUID]
    medical_record: Optional[str]
    is_test: bool
    last_reset: Optional[datetime]


class ParticipantCreate(ParticipantBase):
    pass


class ParticipantUpdate(ParticipantBase):
    id: UUID


class ParticipantPydantic(ParticipantBase):
    id: UUID
    chat_identity: str
    created_at: datetime
    updated_at: datetime
    activities: Optional[List[ParticipantActivityOutput]] = []

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__
        obj_dict["chat_identity"] = obj.chat_identity
        activities = await obj.activities
        if activities:
            obj_dict["activities"] = [activity for activity in activities]
        return cls(**obj_dict)


class ParticipantsList(BaseModel):
    participants: List[ParticipantPydantic] | None


# Pydantic models for SoleraParticipant
class SoleraParticipantBase(BaseModel):
    participant_id: UUID
    solera_id: Optional[UUID]
    solera_key: str
    solera_program_id: str
    solera_enrollment_id: str


class SoleraParticipantCreate(SoleraParticipantBase):
    pass


class SoleraParticipantUpdate(SoleraParticipantBase):
    id: UUID


class SoleraParticipantPydantic(SoleraParticipantBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__
        return cls(**obj_dict)


class SoleraParticipantsList(BaseModel):
    participants: List[SoleraParticipantPydantic] | None


# Pydantic models for HeadsUpParticipant
class HeadsUpParticipantBase(BaseModel):
    participant_id: UUID
    heads_up_token: Optional[str]
    heads_up_id: Optional[str]


class HeadsUpParticipantCreate(HeadsUpParticipantBase):
    pass


class HeadsUpParticipantUpdate(HeadsUpParticipantBase):
    id: UUID


class HeadsUpParticipantPydantic(HeadsUpParticipantBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__
        return cls(**obj_dict)


class HeadsUpParticipantsList(BaseModel):
    participants: List[HeadsUpParticipantPydantic] | None


# Pydantic models for ParticipantMeta
class ParticipantMetaBase(BaseModel):
    participant_id: UUID
    metadata: Optional[str | dict]

    @field_validator("metadata", mode="before")
    def convert_metadata(cls, value):
        if isinstance(value, str):
            try:
                return json.loads(value)
            except Exception as e:
                raise ValueError(
                    f"metadata must be a valid JSON string or a dictionary, error: {e}"
                )
        return value


class ParticipantMetaCreate(ParticipantMetaBase):
    pass


class ParticipantMetaUpdate(ParticipantMetaBase):
    id: UUID


class ParticipantMetaPydantic(ParticipantMetaBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    async def from_orm(cls, obj):
        obj_dict = obj.__dict__
        return cls(**obj_dict)


class MetaParticipantsList(BaseModel):
    participants: List[ParticipantMetaPydantic] | None


class ParticipantClassProgress(BaseModel):
    participant: RawParticipant
    live_session: RawLiveSession
    booking: RawBooking
    host: RawAuthorized
    webinar: RawWebinar
