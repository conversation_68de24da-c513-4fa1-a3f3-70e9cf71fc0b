import re

from datetime import datetime
from strawberry.types import Info

import pendulum

from ciba_participant.helpers.timezone_middleware import TIMEZONE_HEADER

OFFSET_TEMPLATE = r"^[\+\-][0-9]{2}:[0-9]{2}$"


def process_offset(info: Info):
    """
    Validates if tz_offset has the right format, and then returns
    the necessary parts of the offset as a tuple,
    or a tuple of None if invalid.
    """
    tz_offset = info.context.request.headers.get(TIMEZONE_HEADER)

    if tz_offset is not None:
        is_valid = re.search(OFFSET_TEMPLATE, tz_offset) or False

        if is_valid:
            sign = tz_offset[0]
            hours = int(tz_offset[1]) * 10 + int(tz_offset[2])
            minutes = int(tz_offset[4]) * 10 + int(tz_offset[5])

            return (sign, hours, minutes)
    return ("+", 0, 0)


def get_timezone_name(info: Info) -> str:
    """
    Converts the given UTC offset to a timezone name.
    """
    sign, hours, minutes = process_offset(info)

    total_offset_minutes = hours * 60 + minutes
    if sign == "-":
        total_offset_minutes = -total_offset_minutes

    # Create a UTC datetime and apply the offset
    dt = pendulum.datetime(2000, 1, 1, tz="UTC").add(minutes=total_offset_minutes)

    # Find the closest timezone name based on the offset
    return dt.timezone_name or "UTC"


def apply_tz_offset(date: datetime, info: Info) -> datetime:
    """
    Converts from UTC to local time, based on the timezone offset present in request headers.
    """

    sign, hours, minutes = process_offset(info)

    if sign == "+":
        return pendulum.instance(date).add(hours=hours, minutes=minutes)

    return pendulum.instance(date).subtract(hours=hours, minutes=minutes)


def tz_to_utc(date: datetime, info: Info) -> datetime:
    """
    Converts from local time to UTC, based on the timezone offset present in request headers.
    """
    sign, hours, minutes = process_offset(info)

    if sign == "+":
        return pendulum.instance(date).subtract(hours=hours, minutes=minutes)

    return pendulum.instance(date).add(hours=hours, minutes=minutes)
