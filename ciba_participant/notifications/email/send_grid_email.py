import os
import csv
from datetime import datetime
from typing import Optional

import aiofiles
import pendulum
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import (
    Attachment,
    Disposition,
    FileContent,
    FileName,
    FileType,
    Mail,
    Content,
)
import base64
from pathlib import Path
from tortoise.functions import Max

from ciba_participant.classes.models import (
    BookingStatusEnum,
    Booking,
    TopicEnum,
    LiveSession,
)
from ciba_participant.common.aws_handler import (
    get_parameter,
    put_csv_to_s3,
    EmailNotificationEvent,
)
from ciba_participant.common.converters import from_utc_to_pst
from ciba_participant.notifications.email.template_info import (
    COHORT_ENDING_IN_28_DAYS_TEMPLATE_ID,
    COHORT_ENDING_IN_28_DAYS_TEMPLATE_SUBJECT,
    COHORT_ENDED_TEMPLATE_SUBJECT,
    COHORT_ENDED_TEMPLATE_ID,
    TRANSTEK_TRACKING_INFO_TEMPLATE_SUBJECT,
    TRANSTEK_TRACKING_INFO_TEMPLATE_ID,
    WELCOME_TEMPLATE_SUBJECT,
    WELCOME_TEMPLATE_ID,
    CLASS_CANCELED_TEMPLATE_SUBJECT,
    CLASS_CANCELED_TEMPLATE_ID,
    CLASS_IN_24_HOURS_TEMPLATE_SUBJECT,
    CLASS_IN_24_HOURS_TEMPLATE_ID,
    NEW_MODULE_TEMPLATE_ID,
    NEW_MODULE_TEMPLATE_SUBJECT,
    NEW_MODULE_DAY_1_TEMPLATE_ID,
    NEW_MODULE_DAY_1_TEMPLATE_SUBJECT,
    COHORT_STARTING_TOMORROW_TEMPLATE_SUBJECT,
    COHORT_STARTING_TOMORROW_TEMPLATE_ID,
    CLASS_RECORDING_AVAILABLE_TEMPLATE_SUBJECT,
    CLASS_RECORDING_AVAILABLE_TEMPLATE_ID,
    CLASS_BOOKED_TEMPLATE_ID,
    CLASS_BOOKED_TEMPLATE_SUBJECT,
    CLASS_CANCELED_BY_USER_TEMPLATE_ID,
    CLASS_CANCELED_BY_USER_TEMPLATE_SUBJECT,
    CLASSES_UNLOCKED_TEMPLATE_ID,
    CLASSES_UNLOCKED_TEMPLATE_SUBJECT,
)
from ciba_participant.participant.user_service import (
    UserService,
    check_re_enrolled_participant,
)
from ciba_participant.settings import get_settings
from ciba_participant.log.logging import logger
from ciba_participant.notifications.email.data_models import (
    ContentType,
    ScaleToParticpant,
)
from ciba_participant.participant.models import (
    Participant,
    Authorized,
    AutorizedRole,
    ParticipantStatus,
    SoleraParticipant,
)
from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityEnum,
)
from ciba_participant.cohort.models import (
    CohortProgramModules,
    CohortStatusEnum,
    Cohort,
    CohortMembers,
    CohortMembershipStatus,
)
from ciba_participant.utils import decrypt

settings = get_settings()


class EmailHandler:
    def __init__(self):
        self.sg = SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
        self.clean_up = False

    def generate_message(
        self,
        to_emails: tuple | str,
        subject: str,
        content_type: ContentType | str,
        content: Content = None,
        bcc_emails_list: list = None,
        dynamic_template_data: dict = None,
        template_id: str = None,
    ) -> Mail:
        message = Mail(
            from_email=settings.DO_NOT_REPLY,
            to_emails=to_emails,
            subject=subject,
        )
        if bcc_emails_list:
            for bcc_email in bcc_emails_list:
                message.add_bcc(bcc_email)

        if content_type == ContentType.HTML:
            message.add_content(content)

        if dynamic_template_data:
            message.dynamic_template_data = dynamic_template_data

        if template_id:
            message.template_id = template_id

        return message

    def send_email(
        self,
        message: Mail,
    ):
        request_body = message.get()

        try:
            response = self.sg.client.mail.send.post(
                request_body=request_body, timeout=settings.SENDGRID_REQUEST_TIMEOUT
            )
            logger.info(response)
        except Exception as e:
            logger.error(e)

    def generate_attachment(self, attachment_path: Path) -> Attachment:
        attachment = Attachment()
        encoded_file = self.encode_file(str(attachment_path))
        attachment.file_content = FileContent(encoded_file)
        attachment.file_type = FileType(ContentType.CSV)
        attachment.file_name = FileName(attachment_path.name)
        attachment.disposition = Disposition("attachment")
        return attachment

    def encode_file(self, file_path: str) -> str:
        with open(file_path, "rb") as f:
            file_data = f.read()
            return base64.b64encode(file_data).decode()

    async def send_new_participant_email(self):
        subject = "New Participants"

        attachment_path = await get_participants_for_last_24_hours_csv()
        put_csv_to_s3(
            file_path=str(attachment_path),
            bucket_name=settings.AWS_BUCKET_NAME,
            s3_key=f"{str(attachment_path).strip('/tmp')}",
        )

        bcc = await get_admin_emails()

        bcc.remove(settings.DEV_EMAIL) if settings.DEV_EMAIL in bcc else None

        html_content = Content(
            mime_type=ContentType.HTML,
            content=f"<strong>New participant {pendulum.now().strftime('%Y-%m-%d-%H-%M-%S')} {settings.ENV}</strong>",
        )

        message = self.generate_message(
            to_emails=(settings.DEV_EMAIL, "Participant"),
            subject=subject,
            content=html_content,
            content_type=ContentType.HTML,  # type: ignore
            bcc_emails_list=bcc,
        )
        attachment = self.generate_attachment(attachment_path=attachment_path)
        message.add_attachment(attachment)
        logger.info(f"Sending new participant email with attachment {attachment_path}")

        self.send_email(
            message=message,
        )
        os.remove(attachment_path)

        s3_path = f"{str(attachment_path).strip('/tmp')}"

        return s3_path

    async def send_cohorts_ending_tomorrow(self):
        subject = "Cohorts Near End"

        attachment_path = await get_cohorts_ending_tomorrow_csv()
        if not attachment_path:
            logger.info("No cohorts ending tomorrow")
            return None

        put_csv_to_s3(
            file_path=str(attachment_path),
            bucket_name=settings.AWS_BUCKET_NAME,
            s3_key=f"{str(attachment_path).strip('/tmp')}",
        )

        bcc = await get_admin_emails()

        if settings.DEV_EMAIL in bcc:
            bcc.remove(settings.DEV_EMAIL)

        html_content = Content(
            mime_type=ContentType.HTML,
            content=f"<strong>Cohorts Ending on {pendulum.now().add(days=1).format('YYYY-MM-DD')} {settings.ENV}</strong>",
        )

        message = self.generate_message(
            to_emails=(settings.DEV_EMAIL, "Participant"),
            subject=subject,
            content=html_content,
            content_type=ContentType.HTML,  # type: ignore
            bcc_emails_list=bcc,
        )
        attachment = self.generate_attachment(attachment_path=attachment_path)
        message.add_attachment(attachment)
        logger.info(
            f"Sending new participant tomorrow email with attachment {attachment_path}"
        )

        self.send_email(
            message=message,
        )
        os.remove(attachment_path)

        s3_path = f"{str(attachment_path).strip('/tmp')}"

        return s3_path

    async def send_new_module_starting_email(self):
        participants_list = (
            await get_list_of_participants_and_their_new_modules_starting()
        )
        login_url = f"{settings.UI_HOST}/login"
        logger.info(
            f"Sending new module starting email to {len(participants_list)} participants"
        )

        for data in participants_list:
            dynamic_template_data = {
                "first_name": data["first_name"],
                "cta_link": login_url,
            }

            if data["order"] == 1:
                template_id = NEW_MODULE_DAY_1_TEMPLATE_ID
                template_subject = NEW_MODULE_DAY_1_TEMPLATE_SUBJECT

                class_datetime = (
                    data["intro_session"].meeting_start_time
                    if "intro_session" in data
                    else None
                )
                if not class_datetime:
                    logger.warning(f"No intro session for participant {data['email']}")
                    continue

                formatted_time = from_utc_to_pst(
                    started_call=class_datetime, date_format="MM/DD/YYYY hh:mm A zz"
                )

                dynamic_template_data["intro_class_date_time"] = formatted_time

            else:
                template_id = NEW_MODULE_TEMPLATE_ID
                template_subject = NEW_MODULE_TEMPLATE_SUBJECT

                dynamic_template_data["program_modules_title"] = data["title"]
                dynamic_template_data["program_modules_short_title"] = data[
                    "short_title"
                ]

            try:
                logger.info(
                    f"Sending new module starting email for {data['email']}"
                    f" with dynamic data {dynamic_template_data}"
                )
                message = self.generate_message(
                    to_emails=(data["email"], None),
                    subject=template_subject,
                    template_id=template_id,
                    content_type=ContentType.PLAIN_TEXT,  # type: ignore
                    dynamic_template_data=dynamic_template_data,
                )

                self.send_email(
                    message=message,
                )
            except Exception as e:
                logger.error(e)

                continue

    def send_reset_password_email(
        self, email: str, reset_code: str, user_type: str, user_id: str = ""
    ) -> bool:
        """Send reset password email to a participant or patient."""
        TEMPLATE_ID = "d-1747e52e203945d78945a2a8c203e89d"
        TEMPLATE_SUBJECT = "Reset password"

        secrets_prefix = "" if settings.IS_NEW_ENV else f"/{settings.ENV}"

        match user_type:
            case "participant":
                host_param_path = f"{secrets_prefix}/participant/UI_HOST"
                reset_link_body = "reset-password"

            case "patient":
                host_param_path = f"{secrets_prefix}/cibahealth/PATIENT_CLIENT_URL"
                reset_link_body = "forgot-password"

            case _:
                logger.warning(f"Unknown user type: {user_type}")
                return False

        host = get_parameter(host_param_path)
        decrypted_code, _ = decrypt(reset_code)
        username = user_id or email

        reset_link = (
            f"{host}/{reset_link_body}?username={username}&reset_code={decrypted_code}"
        )

        message = self.generate_message(
            to_emails=email,
            subject=TEMPLATE_SUBJECT,
            template_id=TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,  # type: ignore
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data={
                "reset_link": reset_link,
                "reset_code": decrypted_code,
            },
        )

        self.send_email(message)
        logger.info(f"Send reset password email to {user_type} {email}")

        return True

    async def send_welcome_email(self, participant_id):
        """Send confirm email to a signed-up user. Based on program."""
        participant = await Participant.filter(id=participant_id).first()

        logger.info(f"Sending welcome email to participant {participant.email}")

        login_url = f"{settings.UI_HOST}/login"

        message = self.generate_message(
            to_emails=participant.email,
            subject=WELCOME_TEMPLATE_SUBJECT,
            template_id=WELCOME_TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,  # type: ignore
            dynamic_template_data={"cta_link": login_url},
            bcc_emails_list=[settings.DEV_EMAIL],
        )

        self.send_email(message)
        logger.info("Send Welcome email")

    async def new_participant_joined_email(self, participant_id):
        """Send email to admin when a new participant joins."""
        participant = await Participant.filter(id=participant_id).get()
        initials = "".join(
            [part[0].upper() for part in participant.full_name().split(" ")]
        )
        payload = {
            "participant_initials": initials,
            "participant_name": participant.full_name(),
            "participant_profile_url": (
                f"{settings.ADMIN_UI_HOST}/participants/details/"
                f"{participant.id}/summary"
            ),
            "participant_list_url": (f"{settings.ADMIN_UI_HOST}/participants/new"),
            "participant_email": participant.email,
            "participant_mrn": participant.medical_record,
            "participant_company_name": "Solera",
            "participant_member_id": str(participant.member_id),
            "participant_group_id": str(participant.group_id),
        }
        TEMPLATE_ID = "d-b3d0d2ac7a454b93bbdc9378b4a43618"
        TEMPLATE_SUBJECT = "New participant on the platform"
        admin_emails = await get_admin_emails()
        for admin_email in admin_emails:
            generated_message = self.generate_message(
                to_emails=admin_email,
                subject=TEMPLATE_SUBJECT,
                template_id=TEMPLATE_ID,
                content_type=ContentType.PLAIN_TEXT,  # type: ignore
                bcc_emails_list=[settings.DEV_EMAIL],
                dynamic_template_data=payload,
            )
            self.send_email(generated_message)
            logger.info(
                f"Admin {admin_email} has been notified about new participant {participant.id}"
            )
        return True

    async def send_disenrolled_email(self, participant_id):
        """Send email when user has been disenrolled on solera side"""
        TEMPLATE_ID = "d-e9aa37072ed84399bbc5a4ade17d963f"
        TEMPLATE_SUBJECT = "Participant disenrollment email"
        data = await UserService.format_disenroll_email(participant_id=participant_id)

        dynamic_template_data = {
            "email": data["email"],
            "first_name": data["first_name"],
            "program_name": data["program_name"],
            # "program_date": data["program_date"],
            "disenrolledReason": data["disenrolledReason"],
            "disenrollmentDate": data["disenrollmentDate"],
        }
        message = self.generate_message(
            to_emails=data["email"],
            subject=TEMPLATE_SUBJECT,
            template_id=TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,  # type: ignore
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )

        self.send_email(message)
        logger.info("Disenroll email sent successfully")

    def send_cancelled_session_email(self, email, first_name, class_name, class_date):
        """Send email when session was cancelled by health coach"""
        booking_url = f"{settings.UI_HOST}/classes/explore"
        dynamic_template_data = {
            "first_name": first_name,
            "class_name": class_name,
            "class_date": class_date,
            "cta_link": booking_url,
        }

        message = self.generate_message(
            to_emails=email,
            subject=CLASS_CANCELED_TEMPLATE_SUBJECT.format(class_name),
            template_id=CLASS_CANCELED_TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,  # type: ignore
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )

        self.send_email(message)
        logger.info("Cancellation email sent successfully")

    async def send_cohort_ending_in_28_days_email(self):
        """Send email when a cohort is ending in 28 days"""
        participants = await get_participants_in_cohorts_ending_in_28_days()

        for participant in participants:
            dynamic_template_data = {
                "first_name": participant["first_name"],
                "program_name": participant["program_name"],
                "end_date": participant["end_date"],
            }

            message = self.generate_message(
                to_emails=participant["email"],
                subject=COHORT_ENDING_IN_28_DAYS_TEMPLATE_SUBJECT,
                template_id=COHORT_ENDING_IN_28_DAYS_TEMPLATE_ID,
                content_type=ContentType.PLAIN_TEXT,  # type: ignore
                bcc_emails_list=[settings.DEV_EMAIL],
                dynamic_template_data=dynamic_template_data,
            )

            self.send_email(message)

        logger.info(
            f"Sent cohort ending in 28 days email to {len(participants)} participants"
        )

    async def send_cohort_ended_email(self, cohort_id):
        """Send email when a cohort has ended"""
        members = (
            await CohortMembers.filter(
                cohort_id=cohort_id, status=CohortMembershipStatus.ACTIVE
            )
            .prefetch_related("participant__solera_participant")
            .all()
        )

        for member in members:
            active_solera_participant = [
                sp
                for sp in member.participant.solera_participant
                if sp.status == ParticipantStatus.ACTIVE
            ]

            if not active_solera_participant:
                logger.error(
                    f"Participant {member.participant.id} has no active solera participant"
                )
                continue

            active_solera_participant = active_solera_participant[0]

            dynamic_template_data = {
                "first_name": member.participant.first_name,
                "program_name": active_solera_participant.solera_program_id,
            }

            message = self.generate_message(
                to_emails=member.participant.email,
                subject=COHORT_ENDED_TEMPLATE_SUBJECT,
                template_id=COHORT_ENDED_TEMPLATE_ID,
                content_type=ContentType.PLAIN_TEXT,  # type: ignore
                bcc_emails_list=[settings.DEV_EMAIL],
                dynamic_template_data=dynamic_template_data,
            )

            self.send_email(message)

        logger.info(f"Sent cohort ended email to {len(members)} participants")

    async def send_transtek_tracking_info_email(
        self,
        participant_id: str,
        tracking_url: str,
        tracking_number: str,
        carrier: str,
    ):
        """Send email when a transtek tracking info is available"""
        participant = await Participant.filter(id=participant_id).get_or_none()
        if not participant:
            logger.error(f"Participant {participant_id} does not exist")
            raise ValueError(
                f"Failed to send transtek tracking info email, participant {participant_id} does not exist"
            )

        dynamic_template_data = {
            "first_name": participant.first_name,
            "tracking_link": tracking_url,
            "tracking_number": tracking_number,
            "carrier_name": carrier.upper(),
        }

        message = self.generate_message(
            to_emails=participant.email,
            subject=TRANSTEK_TRACKING_INFO_TEMPLATE_SUBJECT,
            template_id=TRANSTEK_TRACKING_INFO_TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )

        self.send_email(message)

        logger.info(
            f"Transtek tracking info email sent to {participant.email} successfully"
        )

    def send_class_in_24_hours_reminder_email(
        self, email: str, first_name: str, class_name: str, class_date: datetime
    ):
        """Send email when a class is scheduled in 24 hours"""
        formatted_time = from_utc_to_pst(
            started_call=class_date, date_format="MM/DD/YYYY hh:mm A zz"
        )

        dynamic_template_data = {
            "first_name": first_name,
            "class_name": class_name,
            "class_time": formatted_time,
        }

        message = self.generate_message(
            to_emails=email,
            subject=CLASS_IN_24_HOURS_TEMPLATE_SUBJECT,
            template_id=CLASS_IN_24_HOURS_TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )

        self.send_email(message)
        logger.info(
            f"Class in 24 hours email, with dynamic data {dynamic_template_data}"
            f" sent to {email} successfully"
        )
        return True

    def send_cohort_starting_tomorrow_email(self, emails: tuple):
        """Send email to users that have a cohort starting tomorrow"""
        login_url = f"{settings.UI_HOST}/login"
        dynamic_template_data = {
            "cta_link": login_url,
        }

        message = self.generate_message(
            to_emails=emails,
            subject=COHORT_STARTING_TOMORROW_TEMPLATE_SUBJECT,
            template_id=COHORT_STARTING_TOMORROW_TEMPLATE_ID,
            content_type=ContentType.PLAIN_TEXT,
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )
        self.send_email(message)

        logger.info(f"Cohort starting tomorrow email sent to {emails} successfully")

    async def send_class_recording_available_email(self, live_session_id: str):
        """Send email when a class recording is available"""
        bookings = (
            await Booking.filter(live_session_id=live_session_id)
            .prefetch_related("participant", "live_session")
            .all()
        )
        cta_link = f"{settings.UI_HOST}/classes/past"

        for booking in bookings:
            if booking.status != BookingStatusEnum.CANCELED:
                class_datetime = from_utc_to_pst(
                    booking.live_session.meeting_start_time
                )
                dynamic_template_data = {
                    "first_name": booking.participant.first_name,
                    "class_name": booking.live_session.title,
                    "class_date": class_datetime.format("MM/DD/YYYY"),
                    "class_time": class_datetime.format("hh:mm A zz"),
                    "cta_link": cta_link,
                }

                message = self.generate_message(
                    to_emails=booking.participant.email,
                    subject=CLASS_RECORDING_AVAILABLE_TEMPLATE_SUBJECT,
                    template_id=CLASS_RECORDING_AVAILABLE_TEMPLATE_ID,
                    content_type=ContentType.PLAIN_TEXT,
                    bcc_emails_list=[settings.DEV_EMAIL],
                    dynamic_template_data=dynamic_template_data,
                )

                self.send_email(message)
                logger.info(
                    f"Class recording available email sent to {booking.participant.email} successfully"
                )

    async def send_participant_class_interaction_email(
        self,
        participant_id: str,
        live_session_id: str,
        event_type: EmailNotificationEvent,
    ):
        """Send email when a participant interacts with a class"""
        participant = await Participant.filter(id=participant_id).first()
        live_session = await LiveSession.filter(id=live_session_id).first()
        if not participant or not live_session:
            raise ValueError(
                "Failed to send participant class interaction email,"
                f" participant {participant_id} or live session {live_session_id} does not exist"
            )

        default_cta_link = f"{settings.UI_HOST}/classes"
        session_datetime_pst = from_utc_to_pst(live_session.meeting_start_time)

        if event_type == EmailNotificationEvent.CLASS_BOOKED:
            dynamic_template_data = {
                "first_name": participant.first_name,
                "class_name": live_session.title,
                "date": session_datetime_pst.format("MM/DD/YYYY"),
                "time": session_datetime_pst.format("hh:mm A zz"),
                "cta_link": default_cta_link + "/upcoming",
            }
            template_id = CLASS_BOOKED_TEMPLATE_ID
            subject = CLASS_BOOKED_TEMPLATE_SUBJECT

        elif event_type == EmailNotificationEvent.CANCELLED_SESSION_BY_USER:
            dynamic_template_data = {
                "first_name": participant.first_name,
                "cta_link": default_cta_link + "/explore",
            }
            template_id = CLASS_CANCELED_BY_USER_TEMPLATE_ID
            subject = CLASS_CANCELED_BY_USER_TEMPLATE_SUBJECT

        elif event_type == EmailNotificationEvent.CLASSES_UNLOCKED:
            dynamic_template_data = {
                "first_name": participant.first_name,
                "cta_link": default_cta_link + "/explore",
            }
            template_id = CLASSES_UNLOCKED_TEMPLATE_ID
            subject = CLASSES_UNLOCKED_TEMPLATE_SUBJECT

        else:
            raise ValueError(
                f"Unknown event type for participant class interaction email: {event_type}"
            )

        message = self.generate_message(
            to_emails=participant.email,
            subject=subject,
            template_id=template_id,
            content_type=ContentType.PLAIN_TEXT,
            bcc_emails_list=[settings.DEV_EMAIL],
            dynamic_template_data=dynamic_template_data,
        )

        self.send_email(message)
        logger.info(
            f"Participant class interaction email event {event_type} sent to {participant.email} successfully"
        )


async def get_admin_emails() -> list[str]:
    """Return list of admin emails."""

    admin_emails = (
        await Authorized.filter(role=AutorizedRole.ADMIN)
        .all()
        .values_list("email", flat=True)
    )
    return admin_emails


def generate_cohort_ending_tomorrow_csv_file(
    headers: list[str], cohorts: list[dict], base_dir: str = "/tmp"
) -> str:
    """
    Generate a CSV file for cohorts ending tomorrow.

    Args:
        headers: List of column headers for the CSV
        cohorts: List of cohort dictionaries to write to CSV
        base_dir: Base directory for file storage

    Returns:
        str: Full path to the created CSV file
    """
    if not headers or not cohorts:
        raise ValueError("Headers and cohorts cannot be empty")

    current_datetime = pendulum.now()
    tomorrow = current_datetime.add(days=1)

    directory = (
        Path(base_dir) / "cohorts_ending" / str(tomorrow.year) / f"{tomorrow.month:02d}"
    )
    directory.mkdir(parents=True, exist_ok=True)

    tomorrow_date = tomorrow.strftime("%Y-%m-%d")
    filename = directory / f"cohorts_ending_{tomorrow_date}.csv"

    try:
        with open(filename, mode="w", newline="", encoding="utf-8") as file:
            writer = csv.DictWriter(file, fieldnames=headers)
            writer.writeheader()
            writer.writerows(cohorts)

    except OSError as e:
        raise OSError(f"Failed to create CSV file {filename}: {e}")

    return str(filename)


def generate_participants_csv_file(
    headers: list, participants: list[ScaleToParticpant]
) -> Path:
    current_datetime = pendulum.now()
    filepath = Path(f"/tmp/enrolled/{current_datetime.year}/{current_datetime.month}/")
    filepath.mkdir(parents=True, exist_ok=True)
    filename = Path(f"{filepath}/{current_datetime.strftime('%Y-%m-%d_%H-%M')}.csv")
    with open(filename, mode="w", newline="", encoding="utf-8") as file:
        writer = csv.DictWriter(file, fieldnames=headers)
        writer.writeheader()
        for participant in participants:
            writer.writerow(participant.model_dump())

    return filename


async def get_participants_for_last_24_hours_csv() -> Path:
    """Retrieve participants enrolled in the last 24 hours and generate a CSV file.

    Returns:
        Path: Path to the generated CSV file
    """
    headers = [
        "created_at",
        "email",
        "phone_number",
        "first_name",
        "last_name",
        "street1",
        "street2",
        "zipCode",
        "city",
        "state",
        "solera_program_id",
        "weight",
        "status",
        "re_enrolled",
    ]

    try:
        # Query for enrollment activities in the last 24 hours
        enrolled = (
            await ParticipantActivity.filter(
                created_at__gte=pendulum.now().subtract(hours=24),
                activity_type=ParticipantActivityEnum.ENROLL,
                participant__status=ParticipantStatus.ACTIVE,
                participant__is_test=False,
            )
            .prefetch_related(
                "participant__solera_participant", "participant__participant_meta"
            )
            .all()
        )

        logger.info(f"Found {len(enrolled)} enrollment activities in the last 24 hours")

        ready_participants = []
        seen_ids = set()

        for activity in enrolled:
            if activity.participant.id in seen_ids:
                continue
            seen_ids.add(activity.participant.id)

            participant_data = await process_participant_for_csv(activity)
            if participant_data:
                ready_participants.append(participant_data)

        participants_csv_path = generate_participants_csv_file(
            headers=headers, participants=ready_participants
        )
        logger.info(
            f"Generated CSV file at {participants_csv_path} with {len(ready_participants)} participants"
        )
        return participants_csv_path
    except Exception as e:
        logger.error(f"Error generating participants CSV: {e}")
        # Create an empty CSV file to avoid breaking the email sending process
        empty_csv_path = Path(
            f"/tmp/empty_participants_{pendulum.now().strftime('%Y-%m-%d-%H-%M-%S')}.csv"
        )
        async with aiofiles.open(empty_csv_path, "w") as f:
            f.write(",".join(headers) + "\n")
        return empty_csv_path


async def get_cohorts_ending_tomorrow_csv():
    """Retrieve cohorts ending tomorrow and generate a CSV file.

    Returns:
        Path: Path to the generated CSV file
    """
    headers = [
        "cohort_name",
        "program_name",
        "cohort_start_date",
        "cohort_end_date",
    ]

    try:
        cohorts_ending_tomorrow = (
            await Cohort.filter(
                status=CohortStatusEnum.ACTIVE.value,
                cohort_end_date__gte=pendulum.now().add(days=1).start_of("day"),
                cohort_end_date__lte=pendulum.now().add(days=1).end_of("day"),
            )
            .annotate(cohort_end_date=Max("program_modules__ended_at"))
            .prefetch_related("program")
            .all()
        )

        ready_cohorts = []
        if not cohorts_ending_tomorrow:
            return None

        for cohort in cohorts_ending_tomorrow:
            ready_cohorts.append(
                {
                    "cohort_name": cohort.name,
                    "program_name": cohort.program.title,
                    "cohort_start_date": cohort.started_at.strftime("%Y-%m-%d"),
                    "cohort_end_date": (await cohort.end_date).strftime("%Y-%m-%d"),
                }
            )

        cohorts_csv_path = generate_cohort_ending_tomorrow_csv_file(
            headers=headers, cohorts=ready_cohorts
        )

        logger.info(
            f"Generated CSV file at {cohorts_csv_path} with {len(ready_cohorts)} cohorts"
        )
        return cohorts_csv_path
    except Exception as e:
        logger.error(f"Error generating cohorts CSV: {e}")
        return None


async def process_participant_for_csv(
    activity: ParticipantActivity,
) -> Optional[ScaleToParticpant]:
    """Process a participant activity and prepare data for CSV.

    Args:
        activity: The enrollment activity record

    Returns:
        ScaleToParticpant object or None if processing fails
    """
    participant = activity.participant

    try:
        # Get the most recent Solera participant record
        solera_participant = get_latest_solera_participant(participant)
        if not solera_participant:
            logger.error(
                f"Participant {participant.id} has no solera participant record"
            )
            return None

        # Get the most recent metadata record
        metadata = get_participant_metadata(participant)
        if not metadata:
            logger.error(f"Participant {participant.id} has no metadata record")
            return None

        # Check if participant is re-enrolled
        re_enrolled = await check_re_enrollment_status(participant)

        # Extract address information with proper error handling
        address_data = extract_address_data(metadata)
        if not address_data:
            logger.error(f"Participant {participant.id} has invalid address data")
            return None

        # Extract phone number
        phone_number = metadata.get("phone_number", "")

        # Create the participant data object
        return ScaleToParticpant(
            created_at=activity.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            email=participant.email,
            phone_number=phone_number,
            first_name=participant.first_name,
            last_name=participant.last_name,
            street1=address_data["street1"],
            street2=address_data["street2"],
            zipCode=address_data["zipCode"],
            city=address_data["city"],
            state=address_data["state"],
            solera_program_id=solera_participant.solera_program_id,
            weight=str(metadata.get("user_reported_weight", "")),
            status=participant.status.value,
            re_enrolled=re_enrolled,  # Uncommented this field
        )
    except Exception as e:
        logger.error(f"Error processing participant {participant.id}: {e}")
        return None


def get_latest_solera_participant(
    participant: Participant,
) -> Optional[SoleraParticipant]:
    """Get the most recent Solera participant record.

    Args:
        participant: The participant object

    Returns:
        The most recent SoleraParticipant record or None
    """
    solera_participants = participant.solera_participant
    if not solera_participants:
        return None

    if len(solera_participants) > 1:
        logger.warning(
            f"Participant {participant.id} has {len(solera_participants)} solera participant records"
        )
        # Sort by created_at to get the most recent one
        return sorted(solera_participants, key=lambda x: x.created_at, reverse=True)[0]

    return solera_participants[0]


def get_participant_metadata(participant: Participant) -> Optional[dict]:
    """Get the participant metadata.

    Args:
        participant: The participant object

    Returns:
        The metadata dictionary or None
    """
    participant_meta = participant.participant_meta
    if not participant_meta:
        return None

    if len(participant_meta) > 1:
        logger.warning(
            f"Participant {participant.id} has {len(participant_meta)} metadata records"
        )
        # Sort by created_at to get the most recent one
        return sorted(participant_meta, key=lambda x: x.created_at, reverse=True)[
            0
        ].metadata

    return participant_meta[0].metadata


async def check_re_enrollment_status(participant: Participant) -> bool:
    """Check if a participant is re-enrolled using multiple methods.

    Args:
        participant: The participant object

    Returns:
        True if the participant is re-enrolled, False otherwise
    """
    # Method 1: Check for deleted emails containing the current email
    deleted_email_exists = await check_re_enrolled_participant(email=participant.email)

    # Method 2: Check if participant has multiple solera_participant records
    multiple_solera_records = len(participant.solera_participant) > 1

    return deleted_email_exists or multiple_solera_records


def extract_address_data(metadata: dict) -> Optional[dict]:
    """Extract address data from metadata with proper error handling.

    Args:
        metadata: The participant metadata dictionary

    Returns:
        Dictionary with address fields or None if invalid
    """
    try:
        if not metadata or "address" not in metadata:
            return None

        address = metadata["address"]
        required_fields = ["street1", "zipCode", "city", "state"]

        # Check if all required fields exist
        if not all(field in address for field in required_fields):
            return None

        # Process street2 field
        street2 = address.get("street2", "")

        return {
            "street1": address["street1"],
            "street2": street2,
            "zipCode": address["zipCode"],
            "city": address["city"],
            "state": address["state"],
        }
    except Exception as e:
        logger.error(f"Error extracting address data: {e}")
        return None


async def get_list_of_participants_and_their_new_modules_starting() -> list[dict]:
    now = pendulum.now()
    start_of_day = now.start_of("day")
    end_of_day = now.end_of("day")

    # Get program modules that started in today
    program_modules = (
        await CohortProgramModules.filter(
            started_at__gte=start_of_day, started_at__lt=end_of_day
        )
        .prefetch_related("cohort", "program_module")
        .all()
    )

    # Extract cohort IDs from the program modules
    cohort_ids = [pm.cohort.id for pm in program_modules]

    if not cohort_ids:
        return []

    # Create mappings for quick lookup
    cohort_name_map = {pm.cohort.id: pm.cohort.name for pm in program_modules}
    module_data_map = {
        pm.cohort.id: {
            "started_at": pm.started_at,
            "title": pm.program_module.title,
            "short_title": pm.program_module.short_title,
            "order": pm.program_module.order,
        }
        for pm in program_modules
    }

    # Fetch active cohort members for these cohorts
    cohort_members = (
        await CohortMembers.filter(
            cohort_id__in=cohort_ids, status=CohortMembershipStatus.ACTIVE
        )
        .prefetch_related("participant__bookings__live_session__webinar")
        .all()
    )

    # Build result using cohort members
    result = []
    for member in cohort_members:
        if member.participant.status == ParticipantStatus.ACTIVE:
            module_data = module_data_map[member.cohort_id]
            intro_session = next(
                (
                    b.live_session
                    for b in member.participant.bookings
                    if b.live_session.webinar.topic == TopicEnum.INTRO_SESSION
                    and b.status != BookingStatusEnum.CANCELED
                ),
                None,
            )
            result.append(
                {
                    "cohort_name": cohort_name_map[member.cohort_id],
                    "started_at": module_data["started_at"],
                    "title": module_data["title"],
                    "short_title": module_data["short_title"],
                    "order": module_data["order"],
                    "email": member.participant.email,
                    "first_name": member.participant.first_name,
                    "last_name": member.participant.last_name,
                    "intro_session": intro_session,
                }
            )

    return result


async def get_participants_in_cohorts_ending_in_28_days() -> list[dict]:
    """
    Returns a list of participant that are in cohort that end in 28 days and the participants in those cohorts.
    """
    days_ahead = 28
    target_date = pendulum.now().add(days=days_ahead)
    target_date_start = target_date.start_of("day")
    target_date_end = target_date.end_of("day")

    cohorts = (
        await Cohort.filter(
            status=CohortStatusEnum.ACTIVE.value,
            cohort_end_date__gte=target_date_start,
            cohort_end_date__lte=target_date_end,
        )
        .annotate(cohort_end_date=Max("program_modules__ended_at"))
        .prefetch_related("program")
        .all()
    )

    cohort_ids = [cohort.id for cohort in cohorts]
    cohort_map = {cohort.id: cohort for cohort in cohorts}

    if not cohort_ids:
        return []

    cohort_members = (
        await CohortMembers.filter(
            cohort_id__in=cohort_ids, status=CohortMembershipStatus.ACTIVE
        )
        .prefetch_related("participant")
        .all()
    )

    result = []
    for member in cohort_members:
        if member.participant.status == ParticipantStatus.ACTIVE:
            cohort = cohort_map[member.cohort_id]

            result.append(
                {
                    "email": member.participant.email,
                    "first_name": member.participant.first_name,
                    "program_name": cohort.program.title,
                    "end_date": (await cohort.end_date).strftime("%Y-%m-%d"),
                }
            )

    return result


async def get_participants_with_class_in_24_hours() -> list[dict]:
    """
    Returns a list of participants that have a class in 24 hours with 30-minute window.
    """
    now = pendulum.now()
    start_window = now.add(hours=24)
    end_window = start_window.add(minutes=30)

    bookings = (
        await Booking.filter(
            live_session__meeting_start_time__gte=start_window,
            live_session__meeting_start_time__lt=end_window,
            status=BookingStatusEnum.BOOKED,
        )
        .prefetch_related("live_session", "participant")
        .all()
    )

    participants = []

    for booking in bookings:
        participant = booking.participant
        session = booking.live_session
        participants.append(
            {
                "email": participant.email,
                "first_name": participant.first_name,
                "class_name": session.title,
                "class_date": session.meeting_start_time,
            }
        )

    return participants


async def get_participants_with_cohort_starting_tomorrow() -> list[dict]:
    """
    Returns a list of participants that are in a cohort starting tomorrow.
    """
    tomorrow = pendulum.now().add(days=1)
    tomorrow_start = tomorrow.start_of("day")
    tomorrow_end = tomorrow.end_of("day")

    cohorts = (
        await Cohort.filter(started_at__gte=tomorrow_start, started_at__lt=tomorrow_end)
        .prefetch_related("participants")
        .all()
    )

    participants = [
        {"email": participant.email}
        for cohort in cohorts
        for participant in cohort.participants
    ]

    return participants
