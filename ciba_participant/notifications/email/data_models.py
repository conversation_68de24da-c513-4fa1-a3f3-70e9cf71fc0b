from typing import Optional

from pydantic import BaseModel


class ContentType:
    PLAIN_TEXT = "text/plain"
    HTML = "text/html"
    CSV = "text/csv"


class ScaleToParticpant(BaseModel):
    created_at: str
    email: str
    phone_number: str
    first_name: str
    last_name: str
    solera_program_id: str
    street1: str
    street2: str
    zipCode: str
    city: str
    state: str
    weight: str
    status: str
    re_enrolled: Optional[bool] = False
