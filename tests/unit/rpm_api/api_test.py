from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mock, patch
from uuid import <PERSON>UI<PERSON>

import pytest
from httpx import <PERSON>TTP<PERSON>rror, HTTPStatusError, Response, codes

from ciba_participant.rpm_api.api import (
    sync_measures,
    pair_transtek_device,
    get_carrier_list,
    update_transtek_tracking_data,
    get_transtek_device_info,
)
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.rpm_api.models import TranstekTrackingData, TranstekDeviceInfo

test_participant_id = UUID("8f844654-e2b1-4d75-bbd7-84d492b327a7")
test_participant_email = "<EMAIL>"
GET_SYNC_DATE = "ciba_participant.rpm_api.api.get_sync_start_date"
POST_METHOD = "ciba_participant.rpm_api.api.AsyncClient.post"
GET_METHOD = "ciba_participant.rpm_api.api.AsyncClient.get"


@pytest.mark.asyncio
async def test_sync_measures_with_api_call_error():
    """
    sync_measures should raise an RPMCallError
    when the endpoint call raises an HTTPError.
    """
    test_error = HTTPError("test network error.")

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=None),
        patch(POST_METHOD, new_callable=AsyncMock, side_effect=test_error),
    ):
        with pytest.raises(RPMCallError):
            await sync_measures(test_participant_id)


@pytest.mark.asyncio
async def test_sync_measures_with_error_in_response():
    """
    sync_measures should raise an RPMCallError
    when the api call return an unsuccessful response.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Invalid device."})
    test_response.status_code = codes.BAD_REQUEST
    test_error = HTTPStatusError(
        "Bad Request", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=None),
        patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response),
    ):
        with pytest.raises(RPMCallError) as expected_error:
            await sync_measures(test_participant_id)

        assert expected_error.value.args[0] == "Invalid device."


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_json, expected_value",
    [
        ({"synced": True}, True),
        ({"synced": False}, False),
    ],
)
async def test_get_latest_data_success(test_json, expected_value):
    """
    get_latest_data should return the latest data from RPM.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value=test_json)

    with (
        patch(GET_SYNC_DATE, new_callable=AsyncMock, return_value=1749168000),
        patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response),
    ):
        actual_value = await sync_measures(test_participant_id)

        assert actual_value.success == expected_value


# Tests for pair_transtek_device function
@pytest.mark.asyncio
async def test_pair_transtek_device_success_with_device_id():
    """
    pair_transtek_device should return pairing result when successful with device_id.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={"status": "paired", "device_id": "test_device_123"}
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await pair_transtek_device(
            participant_id=test_participant_id,
            participant_email=test_participant_email,
            serial_number="test_device_123",
        )

        assert result == {"status": "paired", "device_id": "test_device_123"}


@pytest.mark.asyncio
async def test_pair_transtek_device_success_with_imei():
    """
    pair_transtek_device should return pairing result when successful with imei.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={"status": "paired", "imei": "123456789012345"}
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await pair_transtek_device(
            participant_id=test_participant_id,
            participant_email=test_participant_email,
            imei="123456789012345",
        )

        assert result == {"status": "paired", "imei": "123456789012345"}


@pytest.mark.asyncio
async def test_pair_transtek_device_success_with_both_parameters():
    """
    pair_transtek_device should return pairing result when successful with both device_id and imei.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "status": "paired",
            "device_id": "test_device_123",
            "imei": "123456789012345",
        }
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await pair_transtek_device(
            participant_id=test_participant_id,
            participant_email=test_participant_email,
            serial_number="test_device_123",
            imei="123456789012345",
        )

        assert result == {
            "status": "paired",
            "device_id": "test_device_123",
            "imei": "123456789012345",
        }


@pytest.mark.asyncio
async def test_pair_transtek_device_with_http_status_error():
    """
    pair_transtek_device should raise RPMCallError when API returns HTTP status error.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Device already paired"})
    test_response.status_code = codes.BAD_REQUEST
    test_error = HTTPStatusError(
        "Bad Request", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await pair_transtek_device(
                participant_id=test_participant_id,
                participant_email=test_participant_email,
                serial_number="test_device_123",
            )

        assert expected_error.value.args[0] == "Device already paired"


@pytest.mark.asyncio
async def test_pair_transtek_device_with_http_error():
    """
    pair_transtek_device should raise RPMCallError when HTTP error occurs.
    """
    test_error = HTTPError("Network connection failed")

    with patch(POST_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await pair_transtek_device(
                participant_id=test_participant_id,
                participant_email=test_participant_email,
                serial_number="test_device_123",
            )

        assert (
            expected_error.value.args[0]
            == "An error occurred pairing device through RPM service"
        )


# Tests for get_carrier_list function
@pytest.mark.asyncio
async def test_get_carrier_list_success():
    """
    get_carrier_list should return list of carriers when successful.
    """
    test_carriers = [
        {"id": 1, "name": "FedEx", "code": "fedex"},
        {"id": 2, "name": "UPS", "code": "ups"},
        {"id": 3, "name": "USPS", "code": "usps"},
    ]
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value={"carriers": test_carriers})

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_carrier_list()

        assert result == test_carriers
        assert len(result) == 3
        assert result[0]["name"] == "FedEx"


@pytest.mark.asyncio
async def test_get_carrier_list_success_empty_list():
    """
    get_carrier_list should return empty list when no carriers available.
    """
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value={"carriers": []})

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_carrier_list()

        assert result == []


@pytest.mark.asyncio
async def test_get_carrier_list_with_http_status_error():
    """
    get_carrier_list should raise RPMCallError when API returns HTTP status error.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Service unavailable"})
    test_response.status_code = codes.SERVICE_UNAVAILABLE
    test_error = HTTPStatusError(
        "Service Unavailable", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await get_carrier_list()

        assert expected_error.value.args[0] == "Service unavailable"


@pytest.mark.asyncio
async def test_get_carrier_list_with_http_error():
    """
    get_carrier_list should raise RPMCallError when HTTP error occurs.
    """
    test_error = HTTPError("Connection timeout")

    with patch(GET_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await get_carrier_list()

        assert (
            expected_error.value.args[0]
            == "An error occurred getting carrier list through RPM service"
        )


# Tests for update_transtek_tracking_data function
@pytest.mark.asyncio
async def test_update_transtek_tracking_data_success_with_imei():
    """
    update_transtek_tracking_data should return tracking URL when successful with imei.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="1Z999AA1234567890", carrier="ups"
    )
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "tracking_url": "https://tracking.example.com/track/1Z999AA1234567890"
        }
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await update_transtek_tracking_data(
            imei="123456789012345", tracking_data=tracking_data
        )

        assert result == {
            "tracking_url": "https://tracking.example.com/track/1Z999AA1234567890"
        }


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_success_with_serial_number():
    """
    update_transtek_tracking_data should return tracking URL when successful with serial_number.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="1234567890123456", carrier="fedex"
    )
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "tracking_url": "https://tracking.example.com/track/1234567890123456"
        }
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await update_transtek_tracking_data(
            serial_number="SN123456789", tracking_data=tracking_data
        )

        assert result == {
            "tracking_url": "https://tracking.example.com/track/1234567890123456"
        }


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_success_with_both_identifiers():
    """
    update_transtek_tracking_data should return tracking URL when successful with both imei and serial_number.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="9400111899562123456789", carrier="usps"
    )
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(
        return_value={
            "tracking_url": "https://tracking.example.com/track/9400111899562123456789",
            "status": "updated",
        }
    )

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await update_transtek_tracking_data(
            imei="123456789012345",
            serial_number="SN123456789",
            tracking_data=tracking_data,
        )

        assert result == {
            "tracking_url": "https://tracking.example.com/track/9400111899562123456789",
            "status": "updated",
        }


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_with_http_status_error():
    """
    update_transtek_tracking_data should raise RPMCallError when API returns HTTP status error.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="INVALID_TRACKING", carrier="invalid_carrier"
    )
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Invalid tracking number format"})
    test_response.status_code = codes.BAD_REQUEST
    test_error = HTTPStatusError(
        "Bad Request", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(POST_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await update_transtek_tracking_data(
                imei="123456789012345", tracking_data=tracking_data
            )

        assert expected_error.value.args[0] == "Invalid tracking number format"


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_with_http_error():
    """
    update_transtek_tracking_data should raise RPMCallError when HTTP error occurs.
    """
    tracking_data = TranstekTrackingData(
        tracking_number="1Z999AA1234567890", carrier="ups"
    )
    test_error = HTTPError("Request timeout")

    with patch(POST_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await update_transtek_tracking_data(
                imei="123456789012345", tracking_data=tracking_data
            )

        assert (
            expected_error.value.args[0]
            == "An error occurred updating tracking data through RPM service"
        )


@pytest.mark.asyncio
async def test_update_transtek_tracking_data_with_none_tracking_data():
    """
    update_transtek_tracking_data should handle None tracking_data gracefully.
    """
    # This test verifies the function behavior when tracking_data is None
    # The function should fail when trying to access tracking_data.tracking_number
    with pytest.raises(AttributeError):
        await update_transtek_tracking_data(imei="123456789012345", tracking_data=None)


# Tests for get_transtek_device_info function
@pytest.mark.asyncio
async def test_get_transtek_device_info_success():
    """
    get_transtek_device_info should return device info when successful.
    """
    test_device_info_json = {
        "id": "device_123",
        "device_id": "test_device_123",
        "imei": "123456789012345",
        "model": "BP Monitor",
        "device_type": "transtek",
        "status": "active",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "tracking_number": "1Z999AA1234567890",
        "carrier": "ups",
        "timezone": "UTC",
        "member_id": str(test_participant_id),
    }
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value=test_device_info_json)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_transtek_device_info(participant_id=test_participant_id)

        assert isinstance(result, TranstekDeviceInfo)
        assert result.device_id == "test_device_123"
        assert result.imei == "123456789012345"
        assert result.status == "active"
        assert result.model == "BP Monitor"
        assert result.tracking_number == "1Z999AA1234567890"


@pytest.mark.asyncio
async def test_get_transtek_device_info_success_minimal_response():
    """
    get_transtek_device_info should return device info when successful with minimal required fields.
    """
    test_device_info_json = {
        "id": "minimal_device_456",
        "device_id": "minimal_device",
        "imei": "987654321098765",
        "model": "Basic Monitor",
        "device_type": "transtek",
        "status": "inactive",
        "created_at": "2024-01-10T08:00:00Z",
        "updated_at": "2024-01-10T08:00:00Z",
    }
    test_response = MagicMock(spec=Response)
    test_response.raise_for_status = Mock()
    test_response.json = Mock(return_value=test_device_info_json)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        result = await get_transtek_device_info(participant_id=test_participant_id)

        assert isinstance(result, TranstekDeviceInfo)
        assert result.device_id == "minimal_device"
        assert result.status == "inactive"
        assert result.tracking_number is None
        assert result.carrier is None


@pytest.mark.asyncio
async def test_get_transtek_device_info_with_http_status_error():
    """
    get_transtek_device_info should raise RPMCallError when API returns HTTP status error.
    """
    test_response = MagicMock(spec=Response)
    test_response.json = Mock(return_value={"detail": "Device not found"})
    test_response.status_code = codes.NOT_FOUND
    test_error = HTTPStatusError(
        "Not Found", request=MagicMock(), response=test_response
    )
    test_response.raise_for_status = Mock(side_effect=test_error)

    with patch(GET_METHOD, new_callable=AsyncMock, return_value=test_response):
        with pytest.raises(RPMCallError) as expected_error:
            await get_transtek_device_info(participant_id=test_participant_id)

        assert expected_error.value.args[0] == "Device not found"


@pytest.mark.asyncio
async def test_get_transtek_device_info_with_http_error():
    """
    get_transtek_device_info should raise RPMCallError when HTTP error occurs.
    """
    test_error = HTTPError("Connection timeout")

    with patch(GET_METHOD, new_callable=AsyncMock, side_effect=test_error):
        with pytest.raises(RPMCallError) as expected_error:
            await get_transtek_device_info(participant_id=test_participant_id)

        assert (
            expected_error.value.args[0]
            == "An error occurred getting device info through RPM service"
        )
