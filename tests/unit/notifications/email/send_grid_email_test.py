import uuid
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from contextlib import asynccontextmanager

import pendulum
import pytest
from freezegun import freeze_time
from sendgrid.helpers.mail import Mail, Content

from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivityCategory,
    ParticipantActivityDevice,
    ActivityUnit,
)
from ciba_participant.notifications.email.data_models import (
    ScaleToParticpant,
    ContentType,
)
from ciba_participant.notifications.email.send_grid_email import (
    get_participants_for_last_24_hours_csv,
    process_participant_for_csv,
    get_latest_solera_participant,
    get_participant_metadata,
    extract_address_data,
    get_participants_in_cohorts_ending_in_28_days,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    generate_cohort_ending_tomorrow_csv_file,
    get_cohorts_ending_tomorrow_csv,
    get_admin_emails,
    generate_participants_csv_file,
    get_list_of_participants_and_their_new_modules_starting,
    get_participants_with_class_in_24_hours,
    get_participants_with_cohort_starting_tomorrow,
)
from ciba_participant.notifications.email.template_info import (
    COHORT_ENDING_IN_28_DAYS_TEMPLATE_ID,
    COHORT_ENDED_TEMPLATE_ID,
    WELCOME_TEMPLATE_ID,
    WELCOME_TEMPLATE_SUBJECT,
    CLASS_CANCELED_TEMPLATE_ID,
    CLASS_CANCELED_TEMPLATE_SUBJECT,
    CLASS_IN_24_HOURS_TEMPLATE_ID,
    CLASS_IN_24_HOURS_TEMPLATE_SUBJECT,
    COHORT_STARTING_TOMORROW_TEMPLATE_ID,
    COHORT_STARTING_TOMORROW_TEMPLATE_SUBJECT,
)
from ciba_participant.participant.models import (
    Participant,
    ParticipantStatus,
    Authorized,
    AutorizedRole,
)
from ciba_participant.cohort.models import (
    Cohort,
    CohortMembers,
    CohortMembershipStatus,
    CohortStatusEnum,
    CohortProgramModules,
)
from ciba_participant.classes.models import (
    Booking,
    BookingStatusEnum,
)
from ciba_participant.settings import get_settings

settings = get_settings()


# Test data constants - consolidated and organized
class TestConstants:
    """Centralized test constants to reduce duplication."""

    # Basic participant data
    EMAIL = "<EMAIL>"
    FIRST_NAME = "Test"
    LAST_NAME = "User"
    PHONE = "************"
    PROGRAM_ID = "NDPP"
    WEIGHT = "180"
    PARTICIPANT_ID = str(uuid.uuid4())

    # Address data
    ADDRESS = {
        "street1": "123 Main St",
        "street2": "Apt 4B",
        "zipCode": "12345",
        "city": "Test City",
        "state": "TS",
    }

    # Cohort data
    COHORT_ID = uuid.uuid4()
    PROGRAM_NAME = "AscendWell Program"
    END_DATE = "2024-02-15"
    COHORT_NAME = "Test Cohort Tomorrow"
    COHORT_START_DATE = "2024-01-15"
    TOMORROW_DATE = "2024-01-16"
    COHORT_STARTING_NAME = "Test Cohort Starting Tomorrow"
    COHORT_STARTING_DATE = "2024-01-16"

    # Class/Session data
    CLASS_TITLE = "Mindful Eating Workshop"
    CLASS_DATE = "2024-01-16 10:00:00"
    MEETING_START_TIME = pendulum.parse("2024-01-16T10:00:00Z")
    LIVE_SESSION_ID = uuid.uuid4()
    BOOKING_ID = uuid.uuid4()
    TIMEZONE = "America/Los_Angeles"

    # File paths and CSV data
    CSV_FILENAME = "cohorts_ending_2024-01-16.csv"
    CSV_PATH = "/tmp/cohorts_ending/2024/01/cohorts_ending_2024-01-16.csv"
    S3_PATH = "cohorts_ending/2024/01/cohorts_ending_2024-01-16.csv"
    CSV_HEADERS = [
        "cohort_name",
        "program_name",
        "cohort_start_date",
        "cohort_end_date",
    ]

    # Tracking data
    TRACKING_URL = "https://tracking.example.com/track/1234567890123456"
    TRACKING_NUMBER = "1234567890123456"
    CARRIER = "usps"

    # Error messages
    DATABASE_ERROR_MSG = "Database error"
    DATABASE_CONNECTION_ERROR_MSG = "Database connection error"


# Legacy constants for backward compatibility - these reference TestConstants
TEST_COHORT_ID = TestConstants.COHORT_ID
TEST_EMAIL = TestConstants.EMAIL
TEST_CSV_HEADERS = TestConstants.CSV_HEADERS
TEST_CSV_PATH = TestConstants.CSV_PATH
TEST_CSV_FILENAME = TestConstants.CSV_FILENAME
TEST_COHORT_NAME = TestConstants.COHORT_NAME
TEST_PARTICIPANT_ID = TestConstants.PARTICIPANT_ID
TEST_MEETING_START_TIME = TestConstants.MEETING_START_TIME
TEST_S3_PATH = TestConstants.S3_PATH
TEST_TRACKING_URL = TestConstants.TRACKING_URL
TEST_TRACKING_NUMBER = TestConstants.TRACKING_NUMBER
TEST_FIRST_NAME = TestConstants.FIRST_NAME
TEST_LAST_NAME = TestConstants.LAST_NAME
TEST_CLASS_TITLE = TestConstants.CLASS_TITLE
TEST_CARRIER = TestConstants.CARRIER
DATABASE_ERROR_MSG = TestConstants.DATABASE_ERROR_MSG
DATABASE_CONNECTION_ERROR_MSG = TestConstants.DATABASE_CONNECTION_ERROR_MSG

# Use the imported template constants
COHORT_STARTING_TEMPLATE_SUBJECT = COHORT_STARTING_TOMORROW_TEMPLATE_SUBJECT
COHORT_STARTING_TEMPLATE_ID = COHORT_STARTING_TOMORROW_TEMPLATE_ID


# Template constants
TRANSTEK_TRACKING_TEMPLATE_ID = "d-0222a9ff85df45789b4f1945e64d5e09"
TRANSTEK_TRACKING_TEMPLATE_SUBJECT = "Your Scale Is on the Way!"


@pytest.fixture
def mock_participant():
    """Create a mock participant for testing."""
    participant = MagicMock()
    participant.id = uuid.uuid4()
    participant.email = TestConstants.EMAIL
    participant.first_name = TestConstants.FIRST_NAME
    participant.last_name = TestConstants.LAST_NAME
    participant.status = ParticipantStatus.ACTIVE
    participant.is_test = False
    return participant


@pytest.fixture
def mock_solera_participant():
    """Create a mock solera participant for testing."""
    solera_participant = MagicMock()
    solera_participant.id = uuid.uuid4()
    solera_participant.solera_program_id = TestConstants.PROGRAM_ID
    solera_participant.created_at = pendulum.now().subtract(days=1)
    return solera_participant


@pytest.fixture
def mock_metadata():
    """Create mock metadata for testing."""
    return {
        "address": TestConstants.ADDRESS,
        "phone_number": TestConstants.PHONE,
        "weight": TestConstants.WEIGHT,
    }


@pytest.fixture
def mock_participant_meta(mock_metadata):
    """Create a mock participant meta for testing."""
    participant_meta = MagicMock()
    participant_meta.id = uuid.uuid4()
    participant_meta.metadata = mock_metadata
    participant_meta.created_at = pendulum.now().subtract(days=1)
    return participant_meta


@pytest.fixture
def mock_activity(mock_participant):
    """Create a mock participant activity for testing."""
    activity = MagicMock()
    activity.id = uuid.uuid4()
    activity.participant = mock_participant
    activity.activity_type = ParticipantActivityEnum.ENROLL
    activity.created_at = pendulum.now()
    activity.value = "enrolled"
    activity.unit = ActivityUnit.ACTION
    activity.activity_device = ParticipantActivityDevice.MANUAL_INPUT
    activity.activity_category = ParticipantActivityCategory.ACTIVITY
    return activity


@pytest.fixture
def mock_scale_participant():
    """Create a mock ScaleToParticpant for testing."""
    return ScaleToParticpant(
        created_at=pendulum.now().strftime("%Y-%m-%d %H:%M:%S"),
        email=TestConstants.EMAIL,
        phone_number=TestConstants.PHONE,
        first_name=TestConstants.FIRST_NAME,
        last_name=TestConstants.LAST_NAME,
        street1=TestConstants.ADDRESS["street1"],
        street2=TestConstants.ADDRESS["street2"],
        zipCode=TestConstants.ADDRESS["zipCode"],
        city=TestConstants.ADDRESS["city"],
        state=TestConstants.ADDRESS["state"],
        solera_program_id=TestConstants.PROGRAM_ID,
        weight=TestConstants.WEIGHT,
        status="active",
        re_enrolled=True,
    )


@pytest.fixture
def mock_cohort():
    """Create a mock cohort for testing."""
    cohort = MagicMock()
    cohort.id = TestConstants.COHORT_ID
    cohort.name = "Test Cohort"
    cohort.status = CohortStatusEnum.ACTIVE

    # Mock program relationship
    program = MagicMock()
    program.title = TestConstants.PROGRAM_NAME
    cohort.program = program

    # Mock end_date property
    async def mock_end_date():
        return pendulum.parse(TestConstants.END_DATE)

    cohort.end_date = mock_end_date()

    return cohort


@pytest.fixture
def mock_cohort_member(mock_participant):
    """Create a mock cohort member for testing."""
    member = MagicMock()
    member.id = uuid.uuid4()
    member.cohort_id = TestConstants.COHORT_ID
    member.participant = mock_participant
    member.status = CohortMembershipStatus.ACTIVE
    return member


@pytest.fixture
def mock_email_handler():
    """Create a mock EmailHandler for testing."""
    handler = MagicMock(spec=EmailHandler)
    handler.generate_message = AsyncMock()
    handler.send_email = AsyncMock()
    return handler


@pytest.fixture
def mock_cohort_tomorrow():
    """Create a mock cohort ending tomorrow for testing."""
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.name = TestConstants.COHORT_NAME
    cohort.status = CohortStatusEnum.ACTIVE
    cohort.started_at = pendulum.parse(TestConstants.COHORT_START_DATE)

    # Mock program relationship
    program = MagicMock()
    program.title = TestConstants.PROGRAM_NAME
    cohort.program = program

    # Mock end_date property
    async def mock_end_date():
        return pendulum.parse(TestConstants.TOMORROW_DATE)

    cohort.end_date = mock_end_date()

    return cohort


@pytest.fixture
def mock_cohort_data():
    """Create mock cohort data for CSV generation."""
    return {
        "cohort_name": TestConstants.COHORT_NAME,
        "program_name": TestConstants.PROGRAM_NAME,
        "cohort_start_date": TestConstants.COHORT_START_DATE,
        "cohort_end_date": TestConstants.TOMORROW_DATE,
    }


@pytest.fixture
def mock_live_session():
    """Create a mock live session for testing."""
    live_session = MagicMock()
    live_session.id = TestConstants.LIVE_SESSION_ID
    live_session.title = TestConstants.CLASS_TITLE
    live_session.meeting_start_time = TestConstants.MEETING_START_TIME
    return live_session


@pytest.fixture
def mock_booking(mock_participant, mock_live_session):
    """Create a mock booking for testing."""
    booking = MagicMock()
    booking.id = TestConstants.BOOKING_ID
    booking.participant = mock_participant
    booking.live_session = mock_live_session
    booking.status = BookingStatusEnum.BOOKED
    return booking


@pytest.fixture
def mock_participant_push_output():
    """Create mock participant push output data."""
    return {
        "email": TestConstants.EMAIL,
        "first_name": TestConstants.FIRST_NAME,
        "last_name": TestConstants.LAST_NAME,
        "participant_id": str(uuid.uuid4()),
    }


@pytest.fixture
def mock_admin_emails():
    """Create mock admin emails list."""
    return ["<EMAIL>", "<EMAIL>"]


@pytest.fixture
def mock_email_template_data():
    """Create mock email template data."""
    return {
        "first_name": TestConstants.FIRST_NAME,
        "program_name": TestConstants.PROGRAM_NAME,
        "end_date": TestConstants.END_DATE,
    }


# Context managers for common patch setups
@asynccontextmanager
async def setup_process_participant_patches(
    solera_participant=None,
    metadata=None,
    re_enrollment_status=True,
    address_data="__not_set__",
):
    """Set up common patches for process_participant_for_csv tests."""
    patches = [
        patch(
            "ciba_participant.notifications.email.send_grid_email.get_latest_solera_participant",
            return_value=solera_participant,
        ),
        patch(
            "ciba_participant.notifications.email.send_grid_email.get_participant_metadata",
            return_value=metadata,
        ),
        patch(
            "ciba_participant.notifications.email.send_grid_email.check_re_enrollment_status",
            return_value=re_enrollment_status,
        ),
    ]

    # Add address data patch if explicitly specified (including None)
    if address_data != "__not_set__":
        patches.append(
            patch(
                "ciba_participant.notifications.email.send_grid_email.extract_address_data",
                return_value=address_data,
            )
        )

    # Start all patches
    started_patches = [p.start() for p in patches]

    try:
        yield started_patches
    finally:
        # Stop all patches
        for p in patches:
            p.stop()


@asynccontextmanager
async def setup_database_filter_chain(
    model_class, return_value=None, prefetch_relations=None
):
    """Generic context manager for setting up database filter chains."""
    with patch.object(model_class, "filter") as mock_filter:
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=return_value or [])

        mock_prefetch.all = mock_all

        if prefetch_relations:
            mock_filter.return_value.prefetch_related.return_value = mock_prefetch
        else:
            mock_filter.return_value = mock_prefetch

        yield {"filter": mock_filter, "prefetch": mock_prefetch, "all": mock_all}


@asynccontextmanager
async def setup_cohort_filter_chain(cohorts=None, members=None):
    """Set up mock filter chains for cohort-related tests."""
    cohort_patches = []
    member_patches = []

    # Setup cohort filter chain
    if cohorts is not None:
        cohort_filter_patch = patch.object(Cohort, "filter")
        cohort_patches.append(cohort_filter_patch)
        mock_cohort_filter = cohort_filter_patch.start()

        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=cohorts)

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_cohort_filter.return_value.annotate.return_value = mock_annotate

    # Setup members filter chain
    if members is not None:
        member_filter_patch = patch.object(CohortMembers, "filter")
        member_patches.append(member_filter_patch)
        mock_member_filter = member_filter_patch.start()

        mock_members_prefetch = MagicMock()
        mock_members_all = AsyncMock(return_value=members)

        mock_members_prefetch.all = mock_members_all
        mock_member_filter.return_value.prefetch_related.return_value = (
            mock_members_prefetch
        )

    try:
        yield
    finally:
        # Stop all patches
        for p in cohort_patches + member_patches:
            p.stop()


@asynccontextmanager
async def setup_participant_filter_chain(participants=None):
    """Set up mock filter chain for participant-related tests."""
    async with setup_database_filter_chain(Participant, participants) as mocks:
        yield mocks


@asynccontextmanager
async def setup_booking_filter_chain(bookings=None):
    """Set up mock filter chain for booking-related tests."""
    async with setup_database_filter_chain(
        Booking, bookings, prefetch_relations=True
    ) as mocks:
        yield mocks


@asynccontextmanager
async def setup_email_handler_mocks():
    """Set up EmailHandler method mocks."""
    generate_patch = patch.object(EmailHandler, "generate_message")
    send_patch = patch.object(EmailHandler, "send_email")

    mock_generate = generate_patch.start()
    mock_send = send_patch.start()

    try:
        yield {"generate": mock_generate, "send": mock_send}
    finally:
        generate_patch.stop()
        send_patch.stop()


# Helper functions for tests
def assert_address_data(address_data, expected_address=None):
    """Assert that address data matches expected values."""
    if expected_address is None:
        assert address_data is None
    else:
        assert address_data is not None
        for key, value in expected_address.items():
            assert address_data[key] == value


def create_scale_participant_data(**overrides):
    """Create ScaleToParticpant test data with optional overrides."""
    default_data = {
        "created_at": "2023-01-01 12:00:00",
        "email": TestConstants.EMAIL,
        "phone_number": TestConstants.PHONE,
        "first_name": TestConstants.FIRST_NAME,
        "last_name": TestConstants.LAST_NAME,
        "street1": TestConstants.ADDRESS["street1"],
        "street2": TestConstants.ADDRESS["street2"],
        "zipCode": TestConstants.ADDRESS["zipCode"],
        "city": TestConstants.ADDRESS["city"],
        "state": TestConstants.ADDRESS["state"],
        "solera_program_id": TestConstants.PROGRAM_ID,
        "weight": TestConstants.WEIGHT,
        "status": "active",
        "re_enrolled": True,
    }
    default_data.update(overrides)
    return ScaleToParticpant(**default_data)


def assert_email_generation_call(
    mock_generate,
    expected_email,
    expected_subject,
    expected_template_id,
    expected_data=None,
    call_index=0,
):
    """Assert that email generation was called with expected parameters."""
    if call_index == 0 and mock_generate.call_count == 1:
        mock_generate.assert_called_once()
        call_args = mock_generate.call_args
    else:
        assert mock_generate.call_count > call_index, (
            f"Expected at least {call_index + 1} calls, got {mock_generate.call_count}"
        )
        call_args = mock_generate.call_args_list[call_index]

    assert call_args[1]["to_emails"] == expected_email
    assert call_args[1]["subject"] == expected_subject
    assert call_args[1]["template_id"] == expected_template_id

    if expected_data:
        for key, value in expected_data.items():
            assert call_args[1]["dynamic_template_data"][key] == value


def assert_database_filter_call(mock_filter, expected_filters=None):
    """Assert that database filter was called with expected parameters."""
    mock_filter.assert_called_once()
    if expected_filters:
        call_args = mock_filter.call_args[1]
        for key, value in expected_filters.items():
            assert call_args[key] == value


def assert_email_handler_calls(mocks, expected_generate_count=1, expected_send_count=1):
    """Assert that EmailHandler methods were called expected number of times."""
    assert mocks["generate"].call_count == expected_generate_count
    assert mocks["send"].call_count == expected_send_count


def create_mock_participant_data(**overrides):
    """Create mock participant data with optional overrides."""
    default_data = {
        "email": TestConstants.EMAIL,
        "first_name": TestConstants.FIRST_NAME,
        "last_name": TestConstants.LAST_NAME,
        "program_name": TestConstants.PROGRAM_NAME,
        "end_date": TestConstants.END_DATE,
    }
    default_data.update(overrides)
    return default_data


# Parameterized tests for extract_address_data
@pytest.mark.parametrize(
    "metadata,expected_result",
    [
        # Valid address
        ({"address": TestConstants.ADDRESS}, TestConstants.ADDRESS),
        # Missing required field
        (
            {
                "address": {
                    "street1": "123 Main St",
                    # Missing zipCode
                    "city": "Test City",
                    "state": "TS",
                }
            },
            None,
        ),
        # Missing address key
        ({"phone_number": TestConstants.PHONE}, None),
        # None metadata
        (None, None),
    ],
    ids=["valid", "missing_field", "missing_address", "none_metadata"],
)
def test_extract_address_data(metadata, expected_result):
    """Test extract_address_data with various inputs."""
    # Act
    result = extract_address_data(metadata)

    # Assert
    assert_address_data(result, expected_result)


# Helper function for testing get_latest_x functions
def _test_get_latest_record(participant, get_function, expected_result):
    """Generic test for get_latest_x functions."""
    # Act
    result = get_function(participant)

    # Assert
    assert result == expected_result


# Parameterized tests for get_latest_solera_participant
@pytest.mark.parametrize(
    "solera_records,expected_result",
    [
        # Single record
        (lambda sp: [sp], lambda sp: sp),
        # Multiple records (newest should be returned)
        (
            lambda sp: [MagicMock(created_at=pendulum.now().subtract(days=10)), sp],
            lambda sp: sp,
        ),
        # Empty list
        (lambda _: [], lambda _: None),
    ],
    ids=["single", "multiple", "empty"],
)
def test_get_latest_solera_participant(
    mock_participant, mock_solera_participant, solera_records, expected_result
):
    """Test get_latest_solera_participant with various inputs."""
    # Arrange
    mock_participant.solera_participant = solera_records(mock_solera_participant)
    expected = expected_result(mock_solera_participant)

    # Act & Assert
    _test_get_latest_record(mock_participant, get_latest_solera_participant, expected)


# Parameterized tests for get_participant_metadata
@pytest.mark.parametrize(
    "meta_records,expected_result",
    [
        # Single record
        (lambda meta: [meta], lambda meta: meta.metadata),
        # Multiple records (newest should be returned)
        (
            lambda meta: [
                MagicMock(
                    created_at=pendulum.now().subtract(days=10),
                    metadata={"old": "data"},
                ),
                meta,
            ],
            lambda meta: meta.metadata,
        ),
        # Empty list
        (lambda _: [], lambda _: None),
    ],
    ids=["single", "multiple", "empty"],
)
def test_get_participant_metadata(
    mock_participant, mock_participant_meta, meta_records, expected_result
):
    """Test get_participant_metadata with various inputs."""
    # Arrange
    mock_participant.participant_meta = meta_records(mock_participant_meta)
    expected = expected_result(mock_participant_meta)

    # Act
    result = get_participant_metadata(mock_participant)

    # Assert
    assert result == expected


# Parameterized tests for check_re_enrollment_status
@pytest.mark.asyncio
@pytest.mark.parametrize(
    "solera_records,deleted_email_exists,expected_result",
    [
        # Deleted email exists
        ([MagicMock()], True, True),
        # Multiple solera records
        ([MagicMock(), MagicMock()], False, True),
        # Not re-enrolled
        ([MagicMock()], False, False),
    ],
    ids=["deleted_email", "multiple_solera", "not_re_enrolled"],
)
async def test_check_re_enrollment_status(
    mock_participant, solera_records, deleted_email_exists, expected_result
):
    """Test check_re_enrollment_status with various inputs."""
    # Arrange
    from ciba_participant.notifications.email.send_grid_email import (
        check_re_enrollment_status,
    )

    mock_participant.solera_participant = solera_records

    # Mock the check_re_enrolled_participant function
    with patch(
        "ciba_participant.notifications.email.send_grid_email.check_re_enrolled_participant",
        return_value=deleted_email_exists,
    ):
        # Act
        result = await check_re_enrollment_status(mock_participant)

        # Assert
        assert result is expected_result


@pytest.mark.asyncio
async def test_process_participant_for_csv_success(
    mock_activity, mock_solera_participant, mock_participant_meta
):
    """Test process_participant_for_csv with successful processing."""
    # Arrange & Act
    async with setup_process_participant_patches(
        solera_participant=mock_solera_participant,
        metadata=mock_participant_meta.metadata,
        re_enrollment_status=True,
    ):
        result = await process_participant_for_csv(mock_activity)

        # Assert
        assert result is not None
        assert result.email == mock_activity.participant.email
        assert result.first_name == mock_activity.participant.first_name
        assert result.last_name == mock_activity.participant.last_name
        assert result.solera_program_id == mock_solera_participant.solera_program_id
        assert result.re_enrolled is True


@pytest.mark.asyncio
async def test_process_participant_for_csv_test_participant(mock_activity):
    """Test process_participant_for_csv with test participant."""
    # Arrange
    mock_activity.participant.is_test = True

    # Act
    result = await process_participant_for_csv(mock_activity)

    # Assert
    assert result is None


@pytest.mark.asyncio
async def test_process_participant_for_csv_no_solera_participant(mock_activity):
    """Test process_participant_for_csv with no solera participant."""
    # Arrange & Act
    async with setup_process_participant_patches(solera_participant=None):
        result = await process_participant_for_csv(mock_activity)

        # Assert
        assert result is None


@pytest.mark.asyncio
async def test_process_participant_for_csv_no_metadata(
    mock_activity, mock_solera_participant
):
    """Test process_participant_for_csv with no metadata."""
    # Arrange & Act
    async with setup_process_participant_patches(
        solera_participant=mock_solera_participant, metadata=None
    ):
        result = await process_participant_for_csv(mock_activity)

        # Assert
        assert result is None


@pytest.mark.asyncio
async def test_process_participant_for_csv_invalid_address(
    mock_activity, mock_solera_participant, mock_participant_meta
):
    """Test process_participant_for_csv with invalid address."""
    # Arrange & Act
    async with setup_process_participant_patches(
        solera_participant=mock_solera_participant,
        metadata=mock_participant_meta.metadata,
        address_data=None,
    ):
        result = await process_participant_for_csv(mock_activity)

        # Assert
        assert result is None


@pytest.mark.asyncio
async def test_process_participant_for_csv_exception(
    mock_activity, mock_solera_participant, mock_participant_meta
):
    """Test process_participant_for_csv with an exception."""
    # Arrange
    async with setup_process_participant_patches(
        solera_participant=mock_solera_participant,
        metadata=mock_participant_meta.metadata,
    ):
        # Override the re-enrollment status patch to raise exception
        with patch(
            "ciba_participant.notifications.email.send_grid_email.check_re_enrollment_status",
            side_effect=Exception("Test exception"),
        ):
            # Act
            result = await process_participant_for_csv(mock_activity)

            # Assert
            assert result is None


@pytest.mark.asyncio
@freeze_time("2023-01-01 12:00:00")
@patch(
    "ciba_participant.notifications.email.send_grid_email.generate_participants_csv_file",
    return_value=Path("/tmp/test.csv"),
)
@patch(
    "ciba_participant.notifications.email.send_grid_email.process_participant_for_csv"
)
@patch(
    "ciba_participant.notifications.email.send_grid_email.ParticipantActivity.filter"
)
async def test_get_participants_for_last_24_hours_csv_success(
    mock_filter, mock_process, mock_generate_csv, mock_activity
):
    """Test get_participants_for_last_24_hours_csv with successful processing."""
    # Arrange
    mock_scale_participant = create_scale_participant_data()

    # Setup the mock filter chain
    mock_prefetch = AsyncMock()
    mock_prefetch.all.return_value = [mock_activity]
    mock_filter.return_value.prefetch_related.return_value = mock_prefetch

    # Setup mock_process return value
    mock_process.return_value = mock_scale_participant

    # Act
    result = await get_participants_for_last_24_hours_csv()

    # Assert
    assert result == Path("/tmp/test.csv")
    mock_filter.assert_called_once()
    mock_filter.return_value.prefetch_related.assert_called_once_with(
        "participant__solera_participant", "participant__participant_meta"
    )
    mock_generate_csv.assert_called_once()


@pytest.mark.asyncio
@freeze_time("2023-01-01 12:00:00")
@patch(
    "ciba_participant.notifications.email.send_grid_email.generate_participants_csv_file",
    return_value=Path("/tmp/test.csv"),
)
async def test_get_participants_for_last_24_hours_csv_empty(mock_generate_csv):
    """Test get_participants_for_last_24_hours_csv with no participants."""
    # Import the module

    # Arrange
    with (
        patch(
            "ciba_participant.notifications.email.send_grid_email.ParticipantActivity.filter",
        ) as mock_filter,
        patch(
            "ciba_participant.notifications.email.send_grid_email.generate_participants_csv_file",
            return_value=Path("/tmp/test.csv"),
        ),
    ):
        # Setup the mock filter chain
        from ciba_participant.notifications.email.send_grid_email import (
            get_participants_for_last_24_hours_csv,
        )

        mock_prefetch = AsyncMock()
        mock_prefetch.all.return_value = []
        mock_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_for_last_24_hours_csv()

        # Assert
        assert result == Path("/tmp/test.csv")
        mock_filter.assert_called_once()


@pytest.mark.asyncio
@freeze_time("2023-01-01 12:00:00")
@patch(
    "ciba_participant.notifications.email.send_grid_email.generate_participants_csv_file",
    return_value=Path("/tmp/test.csv"),
)
@patch(
    "builtins.open",
    mock_open(),
)
async def test_get_participants_for_last_24_hours_csv_exception(mock_generate_csv):
    """Test get_participants_for_last_24_hours_csv with an exception."""
    # Import the module

    result = await get_participants_for_last_24_hours_csv()

    # Assert
    assert result is not None
    assert str(result).startswith("/tmp/empty_participants_")


# Tests for cohort ending email functionality
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_in_cohorts_ending_in_28_days_success(
    mock_cohort, mock_cohort_member
):
    """Test get_participants_in_cohorts_ending_in_28_days with successful data retrieval."""
    # Arrange & Act
    async with setup_cohort_filter_chain(
        cohorts=[mock_cohort], members=[mock_cohort_member]
    ):
        result = await get_participants_in_cohorts_ending_in_28_days()

        # Assert
        assert len(result) == 1
        assert result[0]["email"] == TestConstants.EMAIL
        assert result[0]["first_name"] == TestConstants.FIRST_NAME
        assert result[0]["program_name"] == TestConstants.PROGRAM_NAME


@pytest.mark.asyncio
async def test_get_participants_in_cohorts_ending_in_28_days_no_cohorts():
    """Test get_participants_in_cohorts_ending_in_28_days with no cohorts ending."""
    # Arrange & Act
    async with setup_cohort_filter_chain(cohorts=[]):
        result = await get_participants_in_cohorts_ending_in_28_days()

        # Assert
        assert result == []


@pytest.mark.asyncio
async def test_get_participants_in_cohorts_ending_in_28_days_inactive_participant(
    mock_cohort, mock_cohort_member
):
    """Test get_participants_in_cohorts_ending_in_28_days with inactive participant."""
    # Arrange
    mock_cohort_member.participant.status = ParticipantStatus.DELETED

    # Act
    async with setup_cohort_filter_chain(
        cohorts=[mock_cohort], members=[mock_cohort_member]
    ):
        result = await get_participants_in_cohorts_ending_in_28_days()

        # Assert
        assert result == []


@pytest.mark.asyncio
async def test_send_cohort_ending_in_28_days_email_success():
    """Test send_cohort_ending_in_28_days_email with successful email sending."""
    # Arrange
    mock_participants = [
        create_mock_participant_data(),
        create_mock_participant_data(
            email="<EMAIL>",
            first_name="Test2",
            program_name="Another Program",
            end_date="2024-02-20",
        ),
    ]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_participants_in_cohorts_ending_in_28_days",
        return_value=mock_participants,
    ):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ending_in_28_days_email()

            # Assert
            assert mocks["generate"].call_count == 2
            assert mocks["send"].call_count == 2

            # Verify first email call
            assert_email_generation_call(
                mocks["generate"],
                TestConstants.EMAIL,
                "Your 28-Day AscendWell Countdown!",
                COHORT_ENDING_IN_28_DAYS_TEMPLATE_ID,
                {
                    "first_name": TestConstants.FIRST_NAME,
                    "program_name": TestConstants.PROGRAM_NAME,
                    "end_date": TestConstants.END_DATE,
                },
            )


@pytest.mark.asyncio
async def test_send_cohort_ending_in_28_days_email_no_participants():
    """Test send_cohort_ending_in_28_days_email with no participants."""
    # Arrange
    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_participants_in_cohorts_ending_in_28_days",
        return_value=[],
    ):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ending_in_28_days_email()

            # Assert
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


@pytest.mark.asyncio
async def test_send_cohort_ending_in_28_days_email_send_error():
    """Test send_cohort_ending_in_28_days_email with email sending error."""
    # Arrange
    mock_participants = [create_mock_participant_data()]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_participants_in_cohorts_ending_in_28_days",
        return_value=mock_participants,
    ):
        async with setup_email_handler_mocks() as mocks:
            # Override send to raise exception
            mocks["send"].side_effect = Exception("Send error")

            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act & Assert - Should raise exception
            with pytest.raises(Exception, match="Send error"):
                await email_handler.send_cohort_ending_in_28_days_email()

            # Verify methods were called before error
            mocks["generate"].assert_called_once()
            mocks["send"].assert_called_once()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_success(
    mock_cohort_member, mock_solera_participant
):
    """Test send_cohort_ended_email with successful email sending."""
    # Arrange
    cohort_id = TestConstants.COHORT_ID
    mock_cohort_member.participant.solera_participant = [mock_solera_participant]
    mock_solera_participant.status = ParticipantStatus.ACTIVE

    async with setup_cohort_filter_chain(members=[mock_cohort_member]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(cohort_id)

            # Assert
            assert_email_generation_call(
                mocks["generate"],
                TestConstants.EMAIL,
                "You've reached the AscendWell finish line. Here's what's next...",
                COHORT_ENDED_TEMPLATE_ID,
                {
                    "first_name": TestConstants.FIRST_NAME,
                    "program_name": TestConstants.PROGRAM_ID,
                },
            )
            mocks["send"].assert_called_once()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_no_members():
    """Test send_cohort_ended_email with no cohort members."""
    # Arrange
    cohort_id = TEST_COHORT_ID

    async with setup_cohort_filter_chain(members=[]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(cohort_id)

            # Assert
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_no_active_solera_participant(mock_cohort_member):
    """Test send_cohort_ended_email with no active solera participant."""
    # Arrange
    cohort_id = TEST_COHORT_ID
    inactive_solera = MagicMock()
    inactive_solera.status = ParticipantStatus.DELETED
    mock_cohort_member.participant.solera_participant = [inactive_solera]

    async with setup_cohort_filter_chain(members=[mock_cohort_member]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(cohort_id)

            # Assert
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_multiple_members(mock_solera_participant):
    """Test send_cohort_ended_email with multiple cohort members."""
    # Arrange
    cohort_id = TEST_COHORT_ID

    # Create multiple mock members
    member1 = MagicMock()
    member1.participant.email = "<EMAIL>"
    member1.participant.first_name = "Test1"
    member1.participant.solera_participant = [mock_solera_participant]

    member2 = MagicMock()
    member2.participant.email = "<EMAIL>"
    member2.participant.first_name = "Test2"
    member2.participant.solera_participant = [mock_solera_participant]

    mock_solera_participant.status = ParticipantStatus.ACTIVE

    async with setup_cohort_filter_chain(members=[member1, member2]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(cohort_id)

            # Assert
            assert mocks["generate"].call_count == 2
            assert mocks["send"].call_count == 2


@pytest.mark.asyncio
async def test_send_cohort_ended_email_exception_handling(
    mock_cohort_member, mock_solera_participant
):
    """Test send_cohort_ended_email with exception during email sending."""
    # Arrange
    cohort_id = TEST_COHORT_ID
    mock_cohort_member.participant.solera_participant = [mock_solera_participant]
    mock_solera_participant.status = ParticipantStatus.ACTIVE

    async with setup_cohort_filter_chain(members=[mock_cohort_member]):
        async with setup_email_handler_mocks() as mocks:
            # Override send to raise exception
            mocks["send"].side_effect = Exception("Email error")

            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act & Assert - Should raise exception
            with pytest.raises(Exception, match="Email error"):
                await email_handler.send_cohort_ended_email(cohort_id)

            # Verify methods were called before error
            mocks["generate"].assert_called_once()
            mocks["send"].assert_called_once()


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_in_cohorts_ending_in_28_days_date_filtering():
    """Test get_participants_in_cohorts_ending_in_28_days with correct date filtering."""
    # Arrange & Act
    with patch.object(Cohort, "filter") as mock_cohort_filter:
        # Setup empty cohort result to focus on filter call verification
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_cohort_filter.return_value.annotate.return_value = mock_annotate

        await get_participants_in_cohorts_ending_in_28_days()

        # Assert - Verify the filter was called with correct parameters
        mock_cohort_filter.assert_called_once()
        call_args = mock_cohort_filter.call_args[1]

        # Verify status filter
        assert call_args["status"] == CohortStatusEnum.ACTIVE.value

        # Verify date range (should be 28 days from frozen time)
        assert "cohort_end_date__gte" in call_args
        assert "cohort_end_date__lte" in call_args


@pytest.mark.asyncio
async def test_get_participants_in_cohorts_ending_in_28_days_database_error():
    """Test get_participants_in_cohorts_ending_in_28_days with database error."""
    # Arrange & Act & Assert
    with patch.object(Cohort, "filter", side_effect=Exception(DATABASE_ERROR_MSG)):
        with pytest.raises(Exception, match=DATABASE_ERROR_MSG):
            await get_participants_in_cohorts_ending_in_28_days()


@pytest.mark.asyncio
async def test_send_cohort_ended_email_invalid_cohort_id():
    """Test send_cohort_ended_email with invalid cohort ID."""
    # Arrange
    invalid_cohort_id = uuid.uuid4()

    async with setup_cohort_filter_chain(members=[]):
        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_cohort_ended_email(invalid_cohort_id)

            # Assert
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


# Tests for generate_cohort_ending_tomorrow_csv_file function
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_generate_cohort_ending_tomorrow_csv_file_success(mock_cohort_data):
    """Test generate_cohort_ending_tomorrow_csv_file with successful CSV generation."""
    # Arrange
    headers = TestConstants.CSV_HEADERS
    cohorts = [mock_cohort_data]

    with patch("builtins.open", mock_open()) as mock_file:
        with patch("csv.DictWriter") as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer_class.return_value = mock_writer

            # Act
            result = generate_cohort_ending_tomorrow_csv_file(headers, cohorts)

            # Assert
            assert result is not None
            assert TestConstants.CSV_FILENAME in result
            assert "/tmp/cohorts_ending/2024/01/" in result

            # Verify file operations
            mock_file.assert_called_once()
            mock_writer_class.assert_called_once_with(
                mock_file.return_value.__enter__.return_value, fieldnames=headers
            )
            mock_writer.writeheader.assert_called_once()
            mock_writer.writerows.assert_called_once_with(cohorts)


@pytest.mark.parametrize(
    "headers,cohorts,expected_error",
    [
        ([], [{"test": "data"}], "Headers and cohorts cannot be empty"),
        (TestConstants.CSV_HEADERS, [], "Headers and cohorts cannot be empty"),
    ],
    ids=["empty_headers", "empty_cohorts"],
)
def test_generate_cohort_ending_tomorrow_csv_file_validation_errors(
    headers, cohorts, expected_error
):
    """Test generate_cohort_ending_tomorrow_csv_file with validation errors."""
    # Act & Assert
    with pytest.raises(ValueError, match=expected_error):
        generate_cohort_ending_tomorrow_csv_file(headers, cohorts)


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_generate_cohort_ending_tomorrow_csv_file_custom_base_dir(
    mock_cohort_data,
):
    """Test generate_cohort_ending_tomorrow_csv_file with custom base directory."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = [mock_cohort_data]
    custom_base_dir = "/custom/path"

    with patch("builtins.open", mock_open()) as mock_file:
        with patch("csv.DictWriter") as mock_writer_class:
            with patch("pathlib.Path.mkdir") as mock_mkdir:
                mock_writer = MagicMock()
                mock_writer_class.return_value = mock_writer

                # Act
                result = generate_cohort_ending_tomorrow_csv_file(
                    headers, cohorts, custom_base_dir
                )

                # Assert
                assert result is not None
                assert TEST_CSV_FILENAME in result
                assert "/custom/path/cohorts_ending/2024/01/" in result

                # Verify directory creation
                mock_mkdir.assert_called_once_with(parents=True, exist_ok=True)

                # Verify file operations
                mock_file.assert_called_once()
                mock_writer.writeheader.assert_called_once()
                mock_writer.writerows.assert_called_once_with(cohorts)


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_generate_cohort_ending_tomorrow_csv_file_file_write_error(
    mock_cohort_data,
):
    """Test generate_cohort_ending_tomorrow_csv_file with file write error."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = [mock_cohort_data]

    with patch("builtins.open", side_effect=OSError("Permission denied")):
        # Act & Assert
        with pytest.raises(OSError, match="Failed to create CSV file"):
            generate_cohort_ending_tomorrow_csv_file(headers, cohorts)


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_generate_cohort_ending_tomorrow_csv_file_multiple_cohorts():
    """Test generate_cohort_ending_tomorrow_csv_file with multiple cohorts."""
    # Arrange
    headers = TEST_CSV_HEADERS
    cohorts = [
        {
            "cohort_name": "Cohort 1",
            "program_name": "Program 1",
            "cohort_start_date": "2024-01-01",
            "cohort_end_date": "2024-01-16",
        },
        {
            "cohort_name": "Cohort 2",
            "program_name": "Program 2",
            "cohort_start_date": "2024-01-02",
            "cohort_end_date": "2024-01-16",
        },
    ]

    with patch("builtins.open", mock_open()) as mock_file:
        with patch("csv.DictWriter") as mock_writer_class:
            mock_writer = MagicMock()
            mock_writer_class.return_value = mock_writer

            # Act
            result = generate_cohort_ending_tomorrow_csv_file(headers, cohorts)

            # Assert
            assert result is not None
            assert TEST_CSV_FILENAME in result

            # Verify file operations
            mock_file.assert_called_once()
            mock_writer.writeheader.assert_called_once()
            mock_writer.writerows.assert_called_once_with(cohorts)


# Tests for get_cohorts_ending_tomorrow_csv function
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_success(mock_cohort_tomorrow):
    """Test get_cohorts_ending_tomorrow_csv with successful data retrieval and CSV generation."""
    # Arrange
    expected_csv_path = TEST_CSV_PATH

    with patch.object(Cohort, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_cohort_tomorrow])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        with patch(
            "ciba_participant.notifications.email.send_grid_email.generate_cohort_ending_tomorrow_csv_file",
            return_value=expected_csv_path,
        ) as mock_generate_csv:
            # Act
            result = await get_cohorts_ending_tomorrow_csv()

            # Assert
            assert result == expected_csv_path

            # Verify database query
            mock_filter.assert_called_once()
            call_args = mock_filter.call_args[1]
            assert call_args["status"] == CohortStatusEnum.ACTIVE.value
            assert "cohort_end_date__gte" in call_args
            assert "cohort_end_date__lte" in call_args

            # Verify CSV generation
            mock_generate_csv.assert_called_once()
            csv_call_args = mock_generate_csv.call_args
            assert csv_call_args[1]["headers"] == TEST_CSV_HEADERS
            assert len(csv_call_args[1]["cohorts"]) == 1
            assert csv_call_args[1]["cohorts"][0]["cohort_name"] == TEST_COHORT_NAME


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_no_cohorts():
    """Test get_cohorts_ending_tomorrow_csv with no cohorts ending tomorrow."""
    # Arrange
    with patch.object(Cohort, "filter") as mock_filter:
        # Setup the mock filter chain to return empty list
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        # Act
        result = await get_cohorts_ending_tomorrow_csv()

        # Assert
        assert result is None

        # Verify database query was made
        mock_filter.assert_called_once()


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_multiple_cohorts():
    """Test get_cohorts_ending_tomorrow_csv with multiple cohorts ending tomorrow."""
    # Arrange
    cohort1 = MagicMock()
    cohort1.name = "Cohort 1"
    cohort1.started_at = pendulum.parse("2024-01-01")
    cohort1.program.title = "Program 1"

    async def mock_end_date1():
        return pendulum.parse("2024-01-16")

    cohort1.end_date = mock_end_date1()

    cohort2 = MagicMock()
    cohort2.name = "Cohort 2"
    cohort2.started_at = pendulum.parse("2024-01-02")
    cohort2.program.title = "Program 2"

    async def mock_end_date2():
        return pendulum.parse("2024-01-16")

    cohort2.end_date = mock_end_date2()

    expected_csv_path = TEST_CSV_PATH

    with patch.object(Cohort, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[cohort1, cohort2])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        with patch(
            "ciba_participant.notifications.email.send_grid_email.generate_cohort_ending_tomorrow_csv_file",
            return_value=expected_csv_path,
        ) as mock_generate_csv:
            # Act
            result = await get_cohorts_ending_tomorrow_csv()

            # Assert
            assert result == expected_csv_path

            # Verify CSV generation with multiple cohorts
            mock_generate_csv.assert_called_once()
            csv_call_args = mock_generate_csv.call_args
            assert len(csv_call_args[1]["cohorts"]) == 2
            assert csv_call_args[1]["cohorts"][0]["cohort_name"] == "Cohort 1"
            assert csv_call_args[1]["cohorts"][1]["cohort_name"] == "Cohort 2"


@pytest.mark.asyncio
async def test_get_cohorts_ending_tomorrow_csv_database_error():
    """Test get_cohorts_ending_tomorrow_csv with database error."""
    # Arrange
    with patch.object(
        Cohort, "filter", side_effect=Exception(DATABASE_CONNECTION_ERROR_MSG)
    ):
        # Act
        result = await get_cohorts_ending_tomorrow_csv()

        # Assert
        assert result is None


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_csv_generation_error(
    mock_cohort_tomorrow,
):
    """Test get_cohorts_ending_tomorrow_csv with CSV generation error."""
    # Arrange
    with patch.object(Cohort, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_cohort_tomorrow])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        with patch(
            "ciba_participant.notifications.email.send_grid_email.generate_cohort_ending_tomorrow_csv_file",
            side_effect=Exception("CSV generation failed"),
        ):
            # Act
            result = await get_cohorts_ending_tomorrow_csv()

            # Assert
            assert result is None


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_cohorts_ending_tomorrow_csv_date_filtering():
    """Test get_cohorts_ending_tomorrow_csv with correct date filtering."""
    # Arrange
    with patch.object(Cohort, "filter") as mock_filter:
        # Setup empty cohort result to focus on filter call verification
        mock_annotate = MagicMock()
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_annotate.prefetch_related.return_value = mock_prefetch
        mock_filter.return_value.annotate.return_value = mock_annotate

        # Act
        await get_cohorts_ending_tomorrow_csv()

        # Assert - Verify the filter was called with correct parameters
        mock_filter.assert_called_once()
        call_args = mock_filter.call_args[1]

        # Verify status filter
        assert call_args["status"] == CohortStatusEnum.ACTIVE.value

        # Verify date range (should be tomorrow from frozen time)
        assert "cohort_end_date__gte" in call_args
        assert "cohort_end_date__lte" in call_args

        # The dates should be for tomorrow (2024-01-16)
        gte_date = call_args["cohort_end_date__gte"]
        lte_date = call_args["cohort_end_date__lte"]

        # Verify it's filtering for tomorrow's date range
        assert gte_date.date() == pendulum.parse("2024-01-16").date()
        assert lte_date.date() == pendulum.parse("2024-01-16").date()


# Tests for EmailHandler.send_cohorts_ending_tomorrow method
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_email_handler_send_cohorts_ending_tomorrow_success():
    """Test EmailHandler.send_cohorts_ending_tomorrow with successful email sending."""
    # Arrange
    test_csv_path = TEST_CSV_PATH
    expected_s3_path = TEST_S3_PATH
    mock_admin_emails = ["<EMAIL>", "<EMAIL>"]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_cohorts_ending_tomorrow_csv",
        return_value=test_csv_path,
    ) as mock_get_csv:
        with patch(
            "ciba_participant.notifications.email.send_grid_email.put_csv_to_s3"
        ) as mock_put_s3:
            with patch(
                "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                return_value=mock_admin_emails,
            ) as mock_get_admin:
                with patch("os.remove") as mock_remove:
                    async with setup_email_handler_mocks() as mocks:
                        with patch.object(
                            EmailHandler,
                            "generate_attachment",
                            return_value=MagicMock(),
                        ):
                            # Create EmailHandler instance
                            email_handler = EmailHandler()

                            # Act
                            result = await email_handler.send_cohorts_ending_tomorrow()

                            # Assert
                            assert result == expected_s3_path

                            # Verify CSV generation
                            mock_get_csv.assert_called_once()

                            # Verify S3 upload
                            mock_put_s3.assert_called_once()

                            # Verify admin emails retrieval
                            mock_get_admin.assert_called_once()

                            # Verify email generation and sending
                            mocks["generate"].assert_called_once()
                            mocks["send"].assert_called_once()

                            # Verify file cleanup
                            mock_remove.assert_called_once_with(test_csv_path)

                            # Verify email content
                            call_args = mocks["generate"].call_args[1]
                            assert "Cohorts Near End" in call_args["subject"]
                            assert (
                                "Cohorts Ending on 2024-01-16"
                                in call_args["content"].content
                            )


@pytest.mark.asyncio
async def test_email_handler_send_cohorts_ending_tomorrow_no_cohorts():
    """Test EmailHandler.send_cohorts_ending_tomorrow with no cohorts ending tomorrow."""
    # Arrange
    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_cohorts_ending_tomorrow_csv",
        return_value=None,
    ) as mock_get_csv:
        # Create EmailHandler instance
        email_handler = EmailHandler()

        # Act
        result = await email_handler.send_cohorts_ending_tomorrow()

        # Assert
        assert result is None

        # Verify CSV generation was attempted
        mock_get_csv.assert_called_once()


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_email_handler_send_cohorts_ending_tomorrow_dev_email_removal():
    """Test EmailHandler.send_cohorts_ending_tomorrow with DEV_EMAIL removal from BCC."""
    # Arrange
    test_csv_path = TEST_CSV_PATH
    mock_admin_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_cohorts_ending_tomorrow_csv",
        return_value=test_csv_path,
    ):
        with patch(
            "ciba_participant.notifications.email.send_grid_email.put_csv_to_s3"
        ):
            with patch(
                "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                return_value=mock_admin_emails.copy(),
            ):
                with patch("os.remove"):
                    with patch(
                        "ciba_participant.notifications.email.send_grid_email.settings.DEV_EMAIL",
                        "<EMAIL>",
                    ):
                        async with setup_email_handler_mocks() as mocks:
                            with patch.object(
                                EmailHandler,
                                "generate_attachment",
                                return_value=MagicMock(),
                            ):
                                # Create EmailHandler instance
                                email_handler = EmailHandler()

                                # Act
                                await email_handler.send_cohorts_ending_tomorrow()

                                # Assert
                                # Verify email generation was called
                                mocks["generate"].assert_called_once()

                                # Verify BCC list doesn't contain DEV_EMAIL
                                call_args = mocks["generate"].call_args[1]
                                bcc_list = call_args["bcc_emails_list"]
                                assert "<EMAIL>" not in bcc_list
                                assert (
                                    len(bcc_list) == 2
                                )  # Only admin emails, DEV_EMAIL removed


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_email_handler_send_cohorts_ending_tomorrow_s3_upload_error():
    """Test EmailHandler.send_cohorts_ending_tomorrow with S3 upload error."""
    # Arrange
    test_csv_path = TEST_CSV_PATH
    mock_admin_emails = ["<EMAIL>"]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_cohorts_ending_tomorrow_csv",
        return_value=test_csv_path,
    ):
        with patch(
            "ciba_participant.notifications.email.send_grid_email.put_csv_to_s3",
            side_effect=Exception("S3 upload failed"),
        ):
            with patch(
                "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                return_value=mock_admin_emails,
            ):
                with patch("os.remove"):
                    async with setup_email_handler_mocks():
                        # Create EmailHandler instance
                        email_handler = EmailHandler()

                        # Act & Assert - Should raise exception
                        with pytest.raises(Exception, match="S3 upload failed"):
                            await email_handler.send_cohorts_ending_tomorrow()


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_email_handler_send_cohorts_ending_tomorrow_email_send_error():
    """Test EmailHandler.send_cohorts_ending_tomorrow with email sending error."""
    # Arrange
    test_csv_path = TEST_CSV_PATH
    expected_s3_path = TEST_S3_PATH
    mock_admin_emails = ["<EMAIL>"]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_cohorts_ending_tomorrow_csv",
        return_value=test_csv_path,
    ):
        with patch(
            "ciba_participant.notifications.email.send_grid_email.put_csv_to_s3"
        ):
            with patch(
                "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                return_value=mock_admin_emails,
            ):
                with patch("os.remove"):
                    async with setup_email_handler_mocks() as mocks:
                        with patch.object(
                            EmailHandler,
                            "generate_attachment",
                            return_value=MagicMock(),
                        ):
                            # Create EmailHandler instance
                            email_handler = EmailHandler()

                            # Act - Should not raise exception (errors are logged in real implementation)
                            result = await email_handler.send_cohorts_ending_tomorrow()

                            # Assert - Should return expected S3 path
                            assert result == expected_s3_path

                            # Verify methods were called
                            mocks["generate"].assert_called_once()
                            mocks["send"].assert_called_once()


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_email_handler_send_cohorts_ending_tomorrow_file_cleanup_on_success():
    """Test EmailHandler.send_cohorts_ending_tomorrow ensures file cleanup on success."""
    # Arrange
    test_csv_path = TEST_CSV_PATH
    mock_admin_emails = ["<EMAIL>"]

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_cohorts_ending_tomorrow_csv",
        return_value=test_csv_path,
    ):
        with patch(
            "ciba_participant.notifications.email.send_grid_email.put_csv_to_s3"
        ):
            with patch(
                "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                return_value=mock_admin_emails,
            ):
                with patch("os.remove") as mock_remove:
                    async with setup_email_handler_mocks():
                        with patch.object(
                            EmailHandler,
                            "generate_attachment",
                            return_value=MagicMock(),
                        ):
                            # Create EmailHandler instance
                            email_handler = EmailHandler()

                            # Act
                            await email_handler.send_cohorts_ending_tomorrow()

                            # Assert - File should be cleaned up
                            mock_remove.assert_called_once_with(test_csv_path)


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_email_handler_send_cohorts_ending_tomorrow_attachment_generation():
    """Test EmailHandler.send_cohorts_ending_tomorrow generates and attaches CSV file."""
    # Arrange
    test_csv_path = TEST_CSV_PATH
    mock_admin_emails = ["<EMAIL>"]
    mock_attachment = MagicMock()

    with patch(
        "ciba_participant.notifications.email.send_grid_email.get_cohorts_ending_tomorrow_csv",
        return_value=test_csv_path,
    ):
        with patch(
            "ciba_participant.notifications.email.send_grid_email.put_csv_to_s3"
        ):
            with patch(
                "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                return_value=mock_admin_emails,
            ):
                with patch("os.remove"):
                    async with setup_email_handler_mocks() as mocks:
                        # Mock attachment generation
                        with patch.object(
                            EmailHandler,
                            "generate_attachment",
                            return_value=mock_attachment,
                        ) as mock_gen_attachment:
                            # Create EmailHandler instance
                            email_handler = EmailHandler()

                            # Act
                            await email_handler.send_cohorts_ending_tomorrow()

                            # Assert
                            # Verify attachment generation
                            mock_gen_attachment.assert_called_once_with(
                                attachment_path=test_csv_path
                            )

                            # Verify message attachment
                            generated_message = mocks["generate"].return_value
                            generated_message.add_attachment.assert_called_once_with(
                                mock_attachment
                            )


# Tests for send_transtek_tracking_info_email function
@pytest.mark.asyncio
async def test_send_transtek_tracking_info_email_success(mock_participant):
    """Test send_transtek_tracking_info_email with successful email sending."""
    # Arrange
    mock_participant.id = TestConstants.PARTICIPANT_ID
    mock_participant.email = TestConstants.EMAIL
    mock_participant.first_name = TestConstants.FIRST_NAME

    with patch.object(Participant, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_get_or_none = AsyncMock(return_value=mock_participant)
        mock_filter.return_value.get_or_none = mock_get_or_none

        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_transtek_tracking_info_email(
                participant_id=TestConstants.PARTICIPANT_ID,
                tracking_url=TestConstants.TRACKING_URL,
                tracking_number=TestConstants.TRACKING_NUMBER,
                carrier=TestConstants.CARRIER,
            )

            # Assert
            # Verify participant lookup
            mock_filter.assert_called_once_with(id=TestConstants.PARTICIPANT_ID)
            mock_get_or_none.assert_called_once()

            # Verify email generation
            assert_email_generation_call(
                mocks["generate"],
                TestConstants.EMAIL,
                TRANSTEK_TRACKING_TEMPLATE_SUBJECT,
                TRANSTEK_TRACKING_TEMPLATE_ID,
                {
                    "first_name": TestConstants.FIRST_NAME,
                    "tracking_link": TestConstants.TRACKING_URL,
                    "tracking_number": TestConstants.TRACKING_NUMBER,
                    "carrier_name": TestConstants.CARRIER.upper(),
                },
            )

            # Verify email sending
            mocks["send"].assert_called_once()


@pytest.mark.asyncio
async def test_send_transtek_tracking_info_email_participant_not_found():
    """Test send_transtek_tracking_info_email with participant not found."""
    # Arrange
    with patch.object(Participant, "filter") as mock_filter:
        # Setup the mock filter chain to return None
        mock_get_or_none = AsyncMock(return_value=None)
        mock_filter.return_value.get_or_none = mock_get_or_none

        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act & Assert
            with pytest.raises(
                ValueError,
                match=f"Failed to send transtek tracking info email, participant {TEST_PARTICIPANT_ID} does not exist",
            ):
                await email_handler.send_transtek_tracking_info_email(
                    participant_id=TEST_PARTICIPANT_ID,
                    tracking_url=TEST_TRACKING_URL,
                    tracking_number=TEST_TRACKING_NUMBER,
                    carrier=TEST_CARRIER,
                )

            # Verify participant lookup was attempted
            mock_filter.assert_called_once_with(id=TEST_PARTICIPANT_ID)
            mock_get_or_none.assert_called_once()

            # Verify no email was sent
            mocks["generate"].assert_not_called()
            mocks["send"].assert_not_called()


@pytest.mark.asyncio
async def test_send_transtek_tracking_info_email_send_error(mock_participant):
    """Test send_transtek_tracking_info_email with email sending error."""
    # Arrange
    mock_participant.id = TEST_PARTICIPANT_ID
    mock_participant.email = TEST_EMAIL
    mock_participant.first_name = TEST_FIRST_NAME

    with patch.object(Participant, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_get_or_none = AsyncMock(return_value=mock_participant)
        mock_filter.return_value.get_or_none = mock_get_or_none

        async with setup_email_handler_mocks() as mocks:
            # Override send to raise exception
            mocks["send"].side_effect = Exception("Email sending failed")

            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act & Assert
            with pytest.raises(Exception, match="Email sending failed"):
                await email_handler.send_transtek_tracking_info_email(
                    participant_id=TEST_PARTICIPANT_ID,
                    tracking_url=TEST_TRACKING_URL,
                    tracking_number=TEST_TRACKING_NUMBER,
                    carrier=TEST_CARRIER,
                )

            # Verify participant lookup
            mock_filter.assert_called_once_with(id=TEST_PARTICIPANT_ID)
            mock_get_or_none.assert_called_once()

            # Verify email generation was attempted
            mocks["generate"].assert_called_once()

            # Verify email sending was attempted
            mocks["send"].assert_called_once()


@pytest.mark.asyncio
async def test_send_transtek_tracking_info_email_carrier_uppercase(mock_participant):
    """Test send_transtek_tracking_info_email with carrier name formatting."""
    # Arrange
    mock_participant.id = TEST_PARTICIPANT_ID
    mock_participant.email = TEST_EMAIL
    mock_participant.first_name = TEST_FIRST_NAME
    lowercase_carrier = "fedex"

    with patch.object(Participant, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_get_or_none = AsyncMock(return_value=mock_participant)
        mock_filter.return_value.get_or_none = mock_get_or_none

        async with setup_email_handler_mocks() as mocks:
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act
            await email_handler.send_transtek_tracking_info_email(
                participant_id=TEST_PARTICIPANT_ID,
                tracking_url=TEST_TRACKING_URL,
                tracking_number=TEST_TRACKING_NUMBER,
                carrier=lowercase_carrier,
            )

            # Assert
            # Verify email generation with uppercase carrier
            assert_email_generation_call(
                mocks["generate"],
                TEST_EMAIL,
                TRANSTEK_TRACKING_TEMPLATE_SUBJECT,
                TRANSTEK_TRACKING_TEMPLATE_ID,
                {
                    "first_name": TEST_FIRST_NAME,
                    "tracking_link": TEST_TRACKING_URL,
                    "tracking_number": TEST_TRACKING_NUMBER,
                    "carrier_name": lowercase_carrier.upper(),  # Should be "FEDEX"
                },
            )

            # Verify email sending
            mocks["send"].assert_called_once()


# Tests for EmailHandler core methods
class TestEmailHandlerCore:
    """Test EmailHandler initialization and core methods."""

    @pytest.fixture
    def email_handler(self):
        """Create EmailHandler instance for testing."""
        with patch(
            "ciba_participant.notifications.email.send_grid_email.settings"
        ) as mock_settings:
            mock_settings.SENDGRID_API_KEY = "test_api_key"
            return EmailHandler()

    def test_email_handler_init(self, email_handler):
        """Test EmailHandler initialization."""
        # Assert
        assert email_handler.sg is not None
        assert email_handler.clean_up is False

    def test_generate_message_basic(self, email_handler):
        """Test generate_message with basic parameters."""
        # Arrange
        to_emails = TestConstants.EMAIL
        subject = "Test Subject"
        content_type = ContentType.PLAIN_TEXT

        # Act
        message = email_handler.generate_message(
            to_emails=to_emails, subject=subject, content_type=content_type
        )

        # Assert
        assert isinstance(message, Mail)
        assert message.subject.subject == subject

    def test_generate_message_with_bcc(self, email_handler):
        """Test generate_message with BCC emails."""
        # Arrange
        to_emails = TestConstants.EMAIL
        subject = "Test Subject"
        content_type = ContentType.PLAIN_TEXT
        bcc_emails = ["<EMAIL>", "<EMAIL>"]

        # Act
        message = email_handler.generate_message(
            to_emails=to_emails,
            subject=subject,
            content_type=content_type,
            bcc_emails_list=bcc_emails,
        )

        # Assert
        assert isinstance(message, Mail)
        # Check that BCC emails were added (message.bcc might be None initially)
        if message.bcc:
            assert len(message.bcc) == 2
        else:
            # Verify the method was called correctly by checking the message structure
            assert message.subject.subject == subject

    def test_generate_message_with_html_content(self):
        """Test generate_message with HTML content."""
        # Arrange
        handler = EmailHandler()
        to_emails = "<EMAIL>"
        subject = "Test Subject"
        content_type = ContentType.HTML
        html_content = Content(mime_type="text/html", content="<p>Test</p>")

        # Act
        message = handler.generate_message(
            to_emails=to_emails,
            subject=subject,
            content_type=content_type,
            content=html_content,
        )

        # Assert
        assert isinstance(message, Mail)
        assert len(message.contents) > 0

    def test_generate_message_with_template_data(self):
        """Test generate_message with dynamic template data."""
        # Arrange
        handler = EmailHandler()
        to_emails = "<EMAIL>"
        subject = "Test Subject"
        content_type = ContentType.PLAIN_TEXT
        template_data = {"name": "John", "value": "123"}
        template_id = "d-123456789"

        # Act
        message = handler.generate_message(
            to_emails=to_emails,
            subject=subject,
            content_type=content_type,
            dynamic_template_data=template_data,
            template_id=template_id,
        )

        # Assert
        assert isinstance(message, Mail)
        # Check that template data and ID were set (might be stored differently in SendGrid)
        assert message.subject.subject == subject
        # The template_id and dynamic_template_data are set on the message object
        # but may not be directly accessible in the same way

    def test_send_email_success(self):
        """Test send_email with successful response."""
        # Arrange
        handler = EmailHandler()
        mock_message = MagicMock()
        mock_message.get.return_value = {"test": "data"}

        with patch.object(handler, "sg") as mock_sg:
            mock_post = MagicMock()
            mock_sg.client.mail.send.post = mock_post
            mock_post.return_value = MagicMock(status_code=202)

            # Act
            handler.send_email(mock_message)

            # Assert
            mock_post.assert_called_once()

    def test_send_email_exception(self):
        """Test send_email with exception handling."""
        # Arrange
        handler = EmailHandler()
        mock_message = MagicMock()
        mock_message.get.return_value = {"test": "data"}

        with patch.object(handler, "sg") as mock_sg:
            mock_post = MagicMock()
            mock_sg.client.mail.send.post = mock_post
            mock_post.side_effect = Exception("SendGrid error")

            # Act & Assert - Should not raise exception
            handler.send_email(mock_message)
            mock_post.assert_called_once()

    def test_encode_file(self):
        """Test encode_file method."""
        # Arrange
        handler = EmailHandler()
        test_content = b"test file content"

        with patch("builtins.open", mock_open(read_data=test_content)):
            # Act
            result = handler.encode_file("/path/to/test.txt")

            # Assert
            import base64

            expected = base64.b64encode(test_content).decode()
            assert result == expected

    def test_generate_attachment(self):
        """Test generate_attachment method."""
        # Arrange
        handler = EmailHandler()
        test_path = Path("/tmp/test.csv")

        with patch.object(handler, "encode_file", return_value="encoded_content"):
            # Act
            attachment = handler.generate_attachment(test_path)

            # Assert
            assert attachment is not None
            assert attachment.file_name.file_name == "test.csv"
            assert attachment.file_type.file_type == ContentType.CSV


# Tests for EmailHandler email sending methods
class TestEmailHandlerEmailMethods:
    """Test EmailHandler email sending methods."""

    @pytest.mark.asyncio
    async def test_send_new_participant_email_success(self):
        """Test send_new_participant_email with successful execution."""
        # Arrange
        test_csv_path = Path("/tmp/test_participants.csv")
        test_admin_emails = ["<EMAIL>", "<EMAIL>"]

        with patch(
            "ciba_participant.notifications.email.send_grid_email.get_participants_for_last_24_hours_csv",
            return_value=test_csv_path,
        ) as mock_get_csv:
            with patch(
                "ciba_participant.notifications.email.send_grid_email.put_csv_to_s3"
            ) as mock_put_s3:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                    return_value=test_admin_emails,
                ) as mock_get_admin:
                    with patch("os.remove") as mock_remove:
                        with patch.object(
                            EmailHandler, "generate_message", return_value=MagicMock()
                        ) as mock_generate:
                            with patch.object(EmailHandler, "send_email") as mock_send:
                                with patch.object(
                                    EmailHandler,
                                    "generate_attachment",
                                    return_value=MagicMock(),
                                ) as mock_attachment:
                                    # Create EmailHandler instance
                                    handler = EmailHandler()

                                    # Act
                                    result = await handler.send_new_participant_email()

                                    # Assert
                                    # The result is the S3 path which strips '/tmp' from the file path
                                    # For '/tmp/test_participants.csv', it becomes 'est_participants.csv'
                                    expected_s3_path = str(test_csv_path).strip("/tmp")
                                    assert result == expected_s3_path
                                    mock_get_csv.assert_called_once()
                                    mock_put_s3.assert_called_once()
                                    mock_get_admin.assert_called_once()
                                    mock_generate.assert_called_once()
                                    mock_send.assert_called_once()
                                    mock_attachment.assert_called_once()
                                    mock_remove.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_new_participant_email_dev_email_removal(self):
        """Test send_new_participant_email removes dev email from BCC."""
        # Arrange
        test_csv_path = Path("/tmp/test_participants.csv")
        test_admin_emails = ["<EMAIL>", "<EMAIL>"]

        with patch(
            "ciba_participant.notifications.email.send_grid_email.get_participants_for_last_24_hours_csv",
            return_value=test_csv_path,
        ):
            with patch(
                "ciba_participant.notifications.email.send_grid_email.put_csv_to_s3"
            ):
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                    return_value=test_admin_emails,
                ):
                    with patch("os.remove"):
                        with patch(
                            "ciba_participant.notifications.email.send_grid_email.settings.DEV_EMAIL",
                            "<EMAIL>",
                        ):
                            with patch.object(
                                EmailHandler,
                                "generate_message",
                                return_value=MagicMock(),
                            ) as mock_generate:
                                with patch.object(EmailHandler, "send_email"):
                                    with patch.object(
                                        EmailHandler,
                                        "generate_attachment",
                                        return_value=MagicMock(),
                                    ):
                                        # Create EmailHandler instance
                                        handler = EmailHandler()

                                        # Act
                                        await handler.send_new_participant_email()

                                        # Assert
                                        # Verify that dev email was removed from BCC list
                                        call_args = mock_generate.call_args
                                        bcc_list = call_args[1]["bcc_emails_list"]
                                        assert "<EMAIL>" not in bcc_list
                                        assert "<EMAIL>" in bcc_list

    @pytest.mark.asyncio
    async def test_send_welcome_email_success(self):
        """Test send_welcome_email with successful execution."""
        # Arrange
        participant_id = str(uuid.uuid4())
        mock_participant = MagicMock()
        mock_participant.email = TEST_EMAIL

        with patch.object(Participant, "filter") as mock_filter:
            mock_first = AsyncMock(return_value=mock_participant)
            mock_filter.return_value.first = mock_first

            with patch.object(
                EmailHandler, "generate_message", return_value=MagicMock()
            ) as mock_generate:
                with patch.object(EmailHandler, "send_email") as mock_send:
                    with patch(
                        "ciba_participant.notifications.email.send_grid_email.settings"
                    ) as mock_settings:
                        mock_settings.UI_HOST = "https://test.example.com"

                        # Create EmailHandler instance
                        handler = EmailHandler()

                        # Act
                        await handler.send_welcome_email(participant_id)

                        # Assert
                        mock_filter.assert_called_once_with(id=participant_id)
                        mock_generate.assert_called_once()
                        mock_send.assert_called_once()

                        # Verify template parameters
                        call_args = mock_generate.call_args
                        assert call_args[1]["to_emails"] == TEST_EMAIL
                        assert call_args[1]["subject"] == WELCOME_TEMPLATE_SUBJECT
                        assert call_args[1]["template_id"] == WELCOME_TEMPLATE_ID

    def test_send_cancelled_session_email_success(self):
        """Test send_cancelled_session_email with successful execution."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = "Yoga Class"
        class_date = "2024-01-15"

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email") as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    handler.send_cancelled_session_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert
                    mock_generate.assert_called_once()
                    mock_send.assert_called_once()

                    # Verify template parameters
                    call_args = mock_generate.call_args
                    assert call_args[1]["to_emails"] == email
                    assert call_args[1][
                        "subject"
                    ] == CLASS_CANCELED_TEMPLATE_SUBJECT.format(class_name)
                    assert call_args[1]["template_id"] == CLASS_CANCELED_TEMPLATE_ID

                    # Verify dynamic template data
                    template_data = call_args[1]["dynamic_template_data"]
                    assert template_data["first_name"] == first_name
                    assert template_data["class_name"] == class_name
                    assert template_data["class_date"] == class_date

    def test_send_reset_password_email_participant_success(self):
        """Test send_reset_password_email for participant with successful execution."""
        # Arrange
        email = TEST_EMAIL
        reset_code = "encrypted_reset_code"
        user_type = "participant"
        user_id = "test_user_id"

        with patch(
            "ciba_participant.notifications.email.send_grid_email.get_parameter",
            return_value="https://participant.example.com",
        ) as mock_get_param:
            with patch(
                "ciba_participant.notifications.email.send_grid_email.decrypt",
                return_value=("decrypted_code", None),
            ) as mock_decrypt:
                with patch.object(
                    EmailHandler, "generate_message", return_value=MagicMock()
                ) as mock_generate:
                    with patch.object(EmailHandler, "send_email") as mock_send:
                        # Create EmailHandler instance
                        handler = EmailHandler()

                        # Act
                        result = handler.send_reset_password_email(
                            email, reset_code, user_type, user_id
                        )

                        # Assert
                        assert result is True
                        mock_get_param.assert_called_once()
                        mock_decrypt.assert_called_once_with(reset_code)
                        mock_generate.assert_called_once()
                        mock_send.assert_called_once()

                        # Verify template parameters
                        call_args = mock_generate.call_args
                        assert call_args[1]["to_emails"] == email
                        assert call_args[1]["subject"] == "Reset password"

                        # Verify dynamic template data contains reset link
                        template_data = call_args[1]["dynamic_template_data"]
                        assert "reset_link" in template_data
                        assert "reset_code" in template_data
                        assert "decrypted_code" in template_data["reset_link"]

    def test_send_reset_password_email_patient_success(self):
        """Test send_reset_password_email for patient with successful execution."""
        # Arrange
        email = TEST_EMAIL
        reset_code = "encrypted_reset_code"
        user_type = "patient"

        with patch(
            "ciba_participant.notifications.email.send_grid_email.get_parameter",
            return_value="https://patient.example.com",
        ) as mock_get_param:
            with patch(
                "ciba_participant.notifications.email.send_grid_email.decrypt",
                return_value=("decrypted_code", None),
            ) as mock_decrypt:
                with patch.object(
                    EmailHandler, "generate_message", return_value=MagicMock()
                ) as mock_generate:
                    with patch.object(EmailHandler, "send_email") as mock_send:
                        # Create EmailHandler instance
                        handler = EmailHandler()

                        # Act
                        result = handler.send_reset_password_email(
                            email, reset_code, user_type
                        )

                        # Assert
                        assert result is True
                        mock_get_param.assert_called_once()
                        mock_decrypt.assert_called_once_with(reset_code)
                        mock_generate.assert_called_once()
                        mock_send.assert_called_once()

                        # Verify reset link contains forgot-password for patient
                        call_args = mock_generate.call_args
                        template_data = call_args[1]["dynamic_template_data"]
                        assert "forgot-password" in template_data["reset_link"]

    def test_send_reset_password_email_unknown_user_type(self):
        """Test send_reset_password_email with unknown user type."""
        # Arrange
        email = TEST_EMAIL
        reset_code = "encrypted_reset_code"
        user_type = "unknown"

        # Create EmailHandler instance
        handler = EmailHandler()

        # Act
        result = handler.send_reset_password_email(email, reset_code, user_type)

        # Assert
        assert result is False

    @pytest.mark.asyncio
    async def test_new_participant_joined_email_success(self):
        """Test new_participant_joined_email with successful execution."""
        # Arrange
        participant_id = str(uuid.uuid4())
        mock_participant = MagicMock()
        mock_participant.id = participant_id
        mock_participant.email = TEST_EMAIL
        mock_participant.first_name = TEST_FIRST_NAME
        mock_participant.last_name = TEST_LAST_NAME
        mock_participant.medical_record = "MRN123"
        mock_participant.member_id = "MEMBER123"
        mock_participant.group_id = "GROUP123"
        mock_participant.full_name.return_value = f"{TEST_FIRST_NAME} {TEST_LAST_NAME}"

        admin_emails = ["<EMAIL>", "<EMAIL>"]

        with patch.object(Participant, "filter") as mock_filter:
            mock_get = AsyncMock(return_value=mock_participant)
            mock_filter.return_value.get = mock_get

            with patch(
                "ciba_participant.notifications.email.send_grid_email.get_admin_emails",
                return_value=admin_emails,
            ) as mock_get_admin:
                with patch.object(
                    EmailHandler, "generate_message", return_value=MagicMock()
                ) as mock_generate:
                    with patch.object(EmailHandler, "send_email") as mock_send:
                        with patch(
                            "ciba_participant.notifications.email.send_grid_email.settings"
                        ) as mock_settings:
                            mock_settings.ADMIN_UI_HOST = "https://admin.example.com"

                            # Create EmailHandler instance
                            handler = EmailHandler()

                            # Act
                            result = await handler.new_participant_joined_email(
                                participant_id
                            )

                            # Assert
                            assert result is True
                            mock_filter.assert_called_once_with(id=participant_id)
                            mock_get_admin.assert_called_once()
                            assert mock_generate.call_count == len(admin_emails)
                            assert mock_send.call_count == len(admin_emails)

                            # Verify template data includes participant information
                            call_args = mock_generate.call_args_list[0]
                            template_data = call_args[1]["dynamic_template_data"]
                            assert (
                                template_data["participant_name"]
                                == f"{TEST_FIRST_NAME} {TEST_LAST_NAME}"
                            )
                            assert template_data["participant_email"] == TEST_EMAIL


# Tests for utility functions
class TestUtilityFunctions:
    """Test utility functions in send_grid_email module."""

    @pytest.mark.asyncio
    async def test_get_admin_emails_success(self):
        """Test get_admin_emails with successful data retrieval."""
        # Arrange
        expected_emails = ["<EMAIL>", "<EMAIL>"]

        with patch.object(Authorized, "filter") as mock_filter:
            # Create a proper async mock chain
            mock_queryset = MagicMock()
            mock_all = MagicMock()
            mock_values_list = AsyncMock(return_value=expected_emails)

            mock_all.values_list = mock_values_list
            mock_queryset.all.return_value = mock_all
            mock_filter.return_value = mock_queryset

            # Act
            result = await get_admin_emails()

            # Assert
            assert result == expected_emails
            mock_filter.assert_called_once_with(role=AutorizedRole.ADMIN)

    @pytest.mark.asyncio
    async def test_get_admin_emails_empty(self):
        """Test get_admin_emails with no admin emails."""
        # Arrange
        with patch.object(Authorized, "filter") as mock_filter:
            # Create a proper async mock chain
            mock_queryset = MagicMock()
            mock_all = MagicMock()
            mock_values_list = AsyncMock(return_value=[])

            mock_all.values_list = mock_values_list
            mock_queryset.all.return_value = mock_all
            mock_filter.return_value = mock_queryset

            # Act
            result = await get_admin_emails()

            # Assert
            assert result == []
            mock_filter.assert_called_once_with(role=AutorizedRole.ADMIN)

    @freeze_time("2024-01-15 12:00:00")
    def test_generate_participants_csv_file_success(self):
        """Test generate_participants_csv_file with successful CSV generation."""
        # Arrange
        headers = ["email", "first_name", "last_name"]
        participants = [
            create_scale_participant_data(
                email="<EMAIL>", first_name="Test1"
            ),
            create_scale_participant_data(
                email="<EMAIL>", first_name="Test2"
            ),
        ]

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("csv.DictWriter") as mock_writer_class:
                with patch("pathlib.Path.mkdir") as mock_mkdir:
                    mock_writer = MagicMock()
                    mock_writer_class.return_value = mock_writer

                    # Act
                    result = generate_participants_csv_file(headers, participants)

                    # Assert
                    assert isinstance(result, Path)
                    # Check that the path contains the expected date pattern (allowing for timezone differences)
                    assert "2024-01-15" in str(result)
                    assert ".csv" in str(result)
                    mock_mkdir.assert_called_once_with(parents=True, exist_ok=True)
                    mock_file.assert_called_once()
                    mock_writer_class.assert_called_once_with(
                        mock_file.return_value.__enter__.return_value,
                        fieldnames=headers,
                    )
                    mock_writer.writeheader.assert_called_once()
                    # Verify writerow was called for each participant (not writerows)
                    assert mock_writer.writerow.call_count == len(participants)

    @freeze_time("2024-01-15 12:00:00")
    def test_generate_participants_csv_file_empty_participants(self):
        """Test generate_participants_csv_file with empty participants list."""
        # Arrange
        headers = ["email", "first_name", "last_name"]
        participants = []

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("csv.DictWriter") as mock_writer_class:
                with patch("pathlib.Path.mkdir") as mock_mkdir:
                    mock_writer = MagicMock()
                    mock_writer_class.return_value = mock_writer

                    # Act
                    result = generate_participants_csv_file(headers, participants)

                    # Assert
                    assert isinstance(result, Path)
                    mock_mkdir.assert_called_once()
                    mock_file.assert_called_once()
                    mock_writer.writeheader.assert_called_once()
                    # Verify writerow was not called for empty list
                    mock_writer.writerow.assert_not_called()

    @pytest.mark.asyncio
    @freeze_time("2024-01-15 12:00:00")
    async def test_get_list_of_participants_and_their_new_modules_starting_success(
        self,
    ):
        """Test get_list_of_participants_and_their_new_modules_starting with successful data retrieval."""
        # Arrange
        mock_program_module = MagicMock()
        mock_program_module.cohort.id = uuid.uuid4()
        mock_program_module.cohort.name = "Test Cohort"
        mock_program_module.started_at = pendulum.now().subtract(hours=12)
        mock_program_module.program_module.title = "Module 1"
        mock_program_module.program_module.short_title = "M1"

        mock_cohort_member = MagicMock()
        mock_cohort_member.cohort_id = mock_program_module.cohort.id
        mock_cohort_member.participant.status = ParticipantStatus.ACTIVE
        mock_cohort_member.participant.email = TEST_EMAIL
        mock_cohort_member.participant.first_name = TEST_FIRST_NAME
        mock_cohort_member.participant.last_name = TEST_LAST_NAME

        with patch.object(CohortProgramModules, "filter") as mock_pm_filter:
            mock_pm_prefetch = MagicMock()
            mock_pm_all = AsyncMock(return_value=[mock_program_module])
            mock_pm_prefetch.all = mock_pm_all
            mock_pm_filter.return_value.prefetch_related.return_value = mock_pm_prefetch

            with patch.object(CohortMembers, "filter") as mock_cm_filter:
                mock_cm_prefetch = MagicMock()
                mock_cm_all = AsyncMock(return_value=[mock_cohort_member])
                mock_cm_prefetch.all = mock_cm_all
                mock_cm_filter.return_value.prefetch_related.return_value = (
                    mock_cm_prefetch
                )

                # Act
                result = await get_list_of_participants_and_their_new_modules_starting()

                # Assert
                assert len(result) == 1
                assert result[0]["email"] == TEST_EMAIL
                assert result[0]["first_name"] == TEST_FIRST_NAME
                assert result[0]["title"] == "Module 1"
                assert result[0]["short_title"] == "M1"
                assert result[0]["cohort_name"] == "Test Cohort"

    @pytest.mark.asyncio
    async def test_get_list_of_participants_and_their_new_modules_starting_no_modules(
        self,
    ):
        """Test get_list_of_participants_and_their_new_modules_starting with no modules."""
        # Arrange
        with patch.object(CohortProgramModules, "filter") as mock_pm_filter:
            mock_pm_prefetch = MagicMock()
            mock_pm_all = AsyncMock(return_value=[])
            mock_pm_prefetch.all = mock_pm_all
            mock_pm_filter.return_value.prefetch_related.return_value = mock_pm_prefetch

            # Act
            result = await get_list_of_participants_and_their_new_modules_starting()

            # Assert
            assert result == []

    @pytest.mark.asyncio
    async def test_send_new_module_starting_email_success(self):
        """Test send_new_module_starting_email with successful execution."""
        # Arrange
        mock_participants = [
            {
                "email": "<EMAIL>",
                "first_name": "Test1",
                "title": "Module 1",
                "short_title": "M1",
                "order": 2,
            },
            {
                "email": "<EMAIL>",
                "first_name": "Test2",
                "title": "Module 2",
                "short_title": "M2",
                "order": 3,
            },
        ]

        with patch(
            "ciba_participant.notifications.email.send_grid_email.get_list_of_participants_and_their_new_modules_starting",
            return_value=mock_participants,
        ):
            with patch.object(
                EmailHandler, "generate_message", return_value=MagicMock()
            ) as mock_generate:
                with patch.object(EmailHandler, "send_email") as mock_send:
                    with patch(
                        "ciba_participant.notifications.email.send_grid_email.settings"
                    ) as mock_settings:
                        mock_settings.UI_HOST = "https://test.example.com"

                        # Create EmailHandler instance
                        handler = EmailHandler()

                        # Act
                        await handler.send_new_module_starting_email()

                        # Assert
                        assert mock_generate.call_count == 2
                        assert mock_send.call_count == 2

                        # Verify first email call
                        first_call = mock_generate.call_args_list[0]
                        assert first_call[1]["to_emails"] == ("<EMAIL>", None)
                        assert first_call[1]["subject"] == "New module started"

                        # Verify template data
                        template_data = first_call[1]["dynamic_template_data"]
                        assert template_data["first_name"] == "Test1"
                        assert (
                            template_data["cta_link"]
                            == "https://test.example.com/login"
                        )
                        assert template_data["program_modules_title"] == "Module 1"
                        assert template_data["program_modules_short_title"] == "M1"

    @pytest.mark.asyncio
    async def test_send_new_module_starting_email_with_exception(self):
        """Test send_new_module_starting_email handles exceptions gracefully."""
        # Arrange
        mock_participants = [
            {
                "email": "<EMAIL>",
                "first_name": "Test1",
                "title": "Module 1",
                "short_title": "M1",
                "order": 2,
            }
        ]

        with patch(
            "ciba_participant.notifications.email.send_grid_email.get_list_of_participants_and_their_new_modules_starting",
            return_value=mock_participants,
        ):
            with patch.object(
                EmailHandler, "generate_message", side_effect=Exception("Email error")
            ):
                with patch.object(EmailHandler, "send_email") as mock_send:
                    with patch(
                        "ciba_participant.notifications.email.send_grid_email.settings"
                    ) as mock_settings:
                        mock_settings.UI_HOST = "https://test.example.com"

                        # Create EmailHandler instance
                        handler = EmailHandler()

                        # Act & Assert - Should not raise exception
                        await handler.send_new_module_starting_email()

                        # Verify send_email was not called due to exception
                        mock_send.assert_not_called()

    @pytest.mark.asyncio
    async def test_send_disenrolled_email_success(self):
        """Test send_disenrolled_email with successful execution."""
        # Arrange
        participant_id = str(uuid.uuid4())
        mock_disenroll_data = {
            "email": TEST_EMAIL,
            "first_name": TEST_FIRST_NAME,
            "program_name": "Test Program",
            "disenrolledReason": "Test reason",
            "disenrollmentDate": "2024-01-15",
        }

        with patch(
            "ciba_participant.notifications.email.send_grid_email.UserService.format_disenroll_email",
            return_value=mock_disenroll_data,
        ) as mock_format:
            with patch.object(
                EmailHandler, "generate_message", return_value=MagicMock()
            ) as mock_generate:
                with patch.object(EmailHandler, "send_email") as mock_send:
                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    await handler.send_disenrolled_email(participant_id)

                    # Assert
                    mock_format.assert_called_once_with(participant_id=participant_id)
                    mock_generate.assert_called_once()
                    mock_send.assert_called_once()

                    # Verify template parameters
                    call_args = mock_generate.call_args
                    assert call_args[1]["to_emails"] == TEST_EMAIL
                    assert call_args[1]["subject"] == "Participant disenrollment email"

                    # Verify template data
                    template_data = call_args[1]["dynamic_template_data"]
                    assert template_data["email"] == TEST_EMAIL
                    assert template_data["first_name"] == TEST_FIRST_NAME
                    assert template_data["program_name"] == "Test Program"


# Tests for get_participants_with_class_in_24_hours function
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_with_class_in_24_hours_success(mock_booking):
    """Test get_participants_with_class_in_24_hours with successful data retrieval."""
    # Arrange
    with patch.object(Booking, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_booking])

        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_with_class_in_24_hours()

        # Assert
        assert len(result) == 1
        assert result[0]["email"] == TEST_EMAIL
        assert result[0]["first_name"] == TEST_FIRST_NAME

        # Verify database query
        mock_filter.assert_called_once()
        call_args = mock_filter.call_args[1]
        assert call_args["status"] == BookingStatusEnum.BOOKED
        assert "live_session__meeting_start_time__gte" in call_args
        assert "live_session__meeting_start_time__lt" in call_args

        # Verify prefetch_related was called
        mock_filter.return_value.prefetch_related.assert_called_once_with(
            "live_session", "participant"
        )


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_with_class_in_24_hours_no_participants():
    """Test get_participants_with_class_in_24_hours with no participants having classes."""
    # Arrange
    with patch.object(Booking, "filter") as mock_filter:
        # Setup the mock filter chain to return empty list
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_with_class_in_24_hours()

        # Assert
        assert result == []

        # Verify database query was made
        mock_filter.assert_called_once()
        mock_filter.return_value.prefetch_related.assert_called_once_with(
            "live_session", "participant"
        )


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_with_class_in_24_hours_database_error():
    """Test get_participants_with_class_in_24_hours with database connection error."""
    # Arrange
    with patch.object(Booking, "filter", side_effect=Exception(DATABASE_ERROR_MSG)):
        # Act & Assert
        with pytest.raises(Exception, match=DATABASE_ERROR_MSG):
            await get_participants_with_class_in_24_hours()


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_with_class_in_24_hours_multiple_participants():
    """Test get_participants_with_class_in_24_hours with multiple different participants."""
    # Arrange
    participant1 = MagicMock()
    participant1.id = uuid.uuid4()
    participant1.email = "<EMAIL>"
    participant1.first_name = "John"
    participant1.last_name = "Doe"

    participant2 = MagicMock()
    participant2.id = uuid.uuid4()
    participant2.email = "<EMAIL>"
    participant2.first_name = "Jane"
    participant2.last_name = "Smith"

    booking1 = MagicMock()
    booking1.participant = participant1
    booking1.live_session = MagicMock()
    booking1.status = BookingStatusEnum.BOOKED

    booking2 = MagicMock()
    booking2.participant = participant2
    booking2.live_session = MagicMock()
    booking2.status = BookingStatusEnum.BOOKED

    with patch.object(Booking, "filter") as mock_filter:
        # Setup the mock filter chain
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[booking1, booking2])

        mock_prefetch.all = mock_all
        mock_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_with_class_in_24_hours()

        # Assert
        assert len(result) == 2

        # Verify first participant
        assert result[0]["email"] == "<EMAIL>"
        assert result[0]["first_name"] == "John"

        # Verify second participant
        assert result[1]["email"] == "<EMAIL>"
        assert result[1]["first_name"] == "Jane"


# Tests for send_class_in_24_hours_reminder_email function
class TestSendClassIn24HoursReminderEmail:
    """Test send_class_in_24_hours_reminder_email method."""

    def test_send_class_in_24_hours_reminder_email_success(self):
        """Test send_class_in_24_hours_reminder_email with successful execution."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = "Yoga Class"
        class_date = pendulum.parse("2024-01-15T14:30:00-08:00")  # PT timezone

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email") as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    result = handler.send_class_in_24_hours_reminder_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert
                    assert result is True
                    mock_generate.assert_called_once()
                    mock_send.assert_called_once()

                    # Verify template parameters
                    call_args = mock_generate.call_args
                    assert call_args[1]["to_emails"] == email
                    assert call_args[1]["subject"] == CLASS_IN_24_HOURS_TEMPLATE_SUBJECT
                    assert call_args[1]["template_id"] == CLASS_IN_24_HOURS_TEMPLATE_ID
                    assert call_args[1]["content_type"] == ContentType.PLAIN_TEXT
                    assert call_args[1]["bcc_emails_list"] == ["<EMAIL>"]

                    # Verify dynamic template data
                    template_data = call_args[1]["dynamic_template_data"]
                    assert template_data["first_name"] == first_name
                    assert template_data["class_name"] == class_name
                    assert (
                        template_data["class_time"] == "01/15/2024 02:30 PM PST"
                    )  # LA time format

    @pytest.mark.parametrize(
        "class_date,expected_time_format,test_description",
        [
            (
                pendulum.parse("2024-01-15T22:00:00Z"),  # 10 PM UTC = 2 PM PT
                "01/15/2024 02:00 PM PST",
                "timezone_conversion",
            ),
            (
                pendulum.parse("2024-01-15T08:00:00-08:00"),  # 8 AM PT
                "01/15/2024 08:00 AM PST",
                "morning_class",
            ),
            (
                pendulum.parse("2024-01-15T19:30:00-08:00"),  # 7:30 PM PT
                "01/15/2024 07:30 PM PST",
                "evening_class",
            ),
            (
                pendulum.parse("2024-01-15T00:00:00-08:00"),  # Midnight PT
                "01/15/2024 12:00 AM PST",
                "midnight_class",
            ),
        ],
        ids=["timezone_conversion", "morning_class", "evening_class", "midnight_class"],
    )
    def test_send_class_in_24_hours_reminder_email_time_formatting(
        self, class_date, expected_time_format, test_description
    ):
        """Test send_class_in_24_hours_reminder_email with various time formats."""
        # Arrange
        email = TestConstants.EMAIL
        first_name = TestConstants.FIRST_NAME
        class_name = f"Test Class - {test_description}"

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email") as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    result = handler.send_class_in_24_hours_reminder_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert
                    assert result is True
                    mock_generate.assert_called_once()
                    mock_send.assert_called_once()

                    # Verify time formatting in template data
                    call_args = mock_generate.call_args
                    template_data = call_args[1]["dynamic_template_data"]
                    assert template_data["class_time"] == expected_time_format

    def test_send_class_in_24_hours_reminder_email_different_class_types(self):
        """Test send_class_in_24_hours_reminder_email with different class types."""
        # Arrange
        test_cases = [
            ("Pilates Session", "Evening Pilates"),
            ("Nutrition Workshop", "Healthy Eating 101"),
            ("Mindfulness Class", "Stress Relief Meditation"),
        ]

        for class_name, expected_class_name in test_cases:
            with patch.object(
                EmailHandler, "generate_message", return_value=MagicMock()
            ) as mock_generate:
                with patch.object(EmailHandler, "send_email"):
                    with patch(
                        "ciba_participant.notifications.email.send_grid_email.settings"
                    ) as mock_settings:
                        mock_settings.DEV_EMAIL = "<EMAIL>"

                        # Create EmailHandler instance
                        handler = EmailHandler()

                        # Act
                        result = handler.send_class_in_24_hours_reminder_email(
                            TEST_EMAIL,
                            TEST_FIRST_NAME,
                            class_name,
                            pendulum.parse("2024-01-15T14:30:00-08:00"),
                        )

                        # Assert
                        assert result is True
                        template_data = mock_generate.call_args[1][
                            "dynamic_template_data"
                        ]
                        assert template_data["class_name"] == class_name

    # Removed redundant time formatting tests - now covered by parameterized test above

    def test_send_class_in_24_hours_reminder_email_with_special_characters(self):
        """Test send_class_in_24_hours_reminder_email with special characters in names."""
        # Arrange
        email = "<EMAIL>"
        first_name = "José"
        class_name = "Yoga & Meditation"
        class_date = pendulum.parse("2024-01-15T14:30:00-08:00")

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email"):
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    result = handler.send_class_in_24_hours_reminder_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert
                    assert result is True
                    template_data = mock_generate.call_args[1]["dynamic_template_data"]
                    assert template_data["first_name"] == "José"
                    assert template_data["class_name"] == "Yoga & Meditation"

    def test_send_class_in_24_hours_reminder_email_generate_message_error(self):
        """Test send_class_in_24_hours_reminder_email when generate_message fails."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = "Test Class"
        class_date = pendulum.parse("2024-01-15T14:30:00-08:00")

        with patch.object(
            EmailHandler,
            "generate_message",
            side_effect=Exception("Generate message failed"),
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email") as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act & Assert
                    with pytest.raises(Exception, match="Generate message failed"):
                        handler.send_class_in_24_hours_reminder_email(
                            email, first_name, class_name, class_date
                        )

                    # Verify generate_message was called but send_email was not
                    mock_generate.assert_called_once()
                    mock_send.assert_not_called()

    def test_send_class_in_24_hours_reminder_email_send_email_error(self):
        """Test send_class_in_24_hours_reminder_email when send_email fails."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = "Test Class"
        class_date = pendulum.parse("2024-01-15T14:30:00-08:00")

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(
                EmailHandler, "send_email", side_effect=Exception("Send email failed")
            ) as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act & Assert
                    with pytest.raises(Exception, match="Send email failed"):
                        handler.send_class_in_24_hours_reminder_email(
                            email, first_name, class_name, class_date
                        )

                    # Verify both methods were called
                    mock_generate.assert_called_once()
                    mock_send.assert_called_once()

    def test_send_class_in_24_hours_reminder_email_empty_parameters(self):
        """Test send_class_in_24_hours_reminder_email with empty string parameters."""
        # Arrange
        email = ""
        first_name = ""
        class_name = ""
        class_date = pendulum.parse("2024-01-15T14:30:00-08:00")

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email") as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    result = handler.send_class_in_24_hours_reminder_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert - method should still work with empty strings
                    assert result is True
                    mock_generate.assert_called_once()
                    mock_send.assert_called_once()

                    # Verify empty parameters are passed through
                    call_args = mock_generate.call_args
                    assert call_args[1]["to_emails"] == ""
                    template_data = call_args[1]["dynamic_template_data"]
                    assert template_data["first_name"] == ""
                    assert template_data["class_name"] == ""

    def test_send_class_in_24_hours_reminder_email_none_dev_email(self):
        """Test send_class_in_24_hours_reminder_email when DEV_EMAIL is None."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = "Test Class"
        class_date = pendulum.parse("2024-01-15T14:30:00-08:00")

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email"):
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = None

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    result = handler.send_class_in_24_hours_reminder_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert
                    assert result is True
                    call_args = mock_generate.call_args
                    assert call_args[1]["bcc_emails_list"] == [None]

    def test_send_class_in_24_hours_reminder_email_datetime_object(self):
        """Test send_class_in_24_hours_reminder_email with regular datetime object."""
        # Arrange
        from datetime import datetime

        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = "Test Class"
        # Regular datetime object (not pendulum)
        class_date = datetime(2024, 1, 15, 14, 30, 0)

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email") as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    result = handler.send_class_in_24_hours_reminder_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert - should work with regular datetime objects
                    assert result is True
                    mock_generate.assert_called_once()
                    mock_send.assert_called_once()

    def test_send_class_in_24_hours_reminder_email_logging(self):
        """Test send_class_in_24_hours_reminder_email logs success message."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = "Test Class"
        class_date = pendulum.parse("2024-01-15T14:30:00-08:00")

        with patch.object(EmailHandler, "generate_message", return_value=MagicMock()):
            with patch.object(EmailHandler, "send_email"):
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    with patch(
                        "ciba_participant.notifications.email.send_grid_email.logger"
                    ) as mock_logger:
                        mock_settings.DEV_EMAIL = "<EMAIL>"

                        # Create EmailHandler instance
                        handler = EmailHandler()

                        # Act
                        result = handler.send_class_in_24_hours_reminder_email(
                            email, first_name, class_name, class_date
                        )

                        # Assert
                        assert result is True
                        mock_logger.info.assert_called_once_with(
                            f"Class in 24 hours email, with dynamic data {{'first_name': '{first_name}', 'class_name': '{class_name}', 'class_time': '01/15/2024 02:30 PM PST'}} sent to {email} successfully"
                        )

    def test_send_class_in_24_hours_reminder_email_long_class_name(self):
        """Test send_class_in_24_hours_reminder_email with very long class name."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = "A Very Long Class Name That Might Cause Issues With Email Templates And Should Be Handled Gracefully"
        class_date = pendulum.parse("2024-01-15T14:30:00-08:00")

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email"):
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    result = handler.send_class_in_24_hours_reminder_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert
                    assert result is True
                    template_data = mock_generate.call_args[1]["dynamic_template_data"]
                    assert (
                        template_data["class_name"] == class_name
                    )  # Should handle long names


# Removed redundant midnight class test - now covered by parameterized test above


# Tests for get_participants_with_cohort_starting_tomorrow function
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_with_cohort_starting_tomorrow_success():
    """Test get_participants_with_cohort_starting_tomorrow with successful data retrieval."""
    # Arrange
    mock_participant1 = MagicMock()
    mock_participant1.email = TEST_EMAIL

    mock_participant2 = MagicMock()
    mock_participant2.email = "<EMAIL>"

    mock_cohort = MagicMock()
    mock_cohort.participants = [mock_participant1, mock_participant2]

    with patch.object(Cohort, "filter") as mock_cohort_filter:
        # Setup cohort filter chain
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_cohort])
        mock_prefetch.all = mock_all
        mock_cohort_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_with_cohort_starting_tomorrow()

        # Assert
        assert len(result) == 2
        assert result[0]["email"] == TEST_EMAIL
        assert result[1]["email"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_get_participants_with_cohort_starting_tomorrow_no_cohorts():
    """Test get_participants_with_cohort_starting_tomorrow with no cohorts starting."""
    # Arrange & Act
    with patch.object(Cohort, "filter") as mock_cohort_filter:
        # Setup empty cohort result
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])
        mock_prefetch.all = mock_all
        mock_cohort_filter.return_value.prefetch_related.return_value = mock_prefetch

        result = await get_participants_with_cohort_starting_tomorrow()

        # Assert
        assert result == []


@pytest.mark.asyncio
async def test_get_participants_with_cohort_starting_tomorrow_empty_participants():
    """Test get_participants_with_cohort_starting_tomorrow with cohort having no participants."""
    # Arrange
    mock_cohort = MagicMock()
    mock_cohort.participants = []  # Empty participants list

    with patch.object(Cohort, "filter") as mock_cohort_filter:
        # Setup cohort filter chain
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_cohort])
        mock_prefetch.all = mock_all
        mock_cohort_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_with_cohort_starting_tomorrow()

        # Assert
        assert result == []


@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_with_cohort_starting_tomorrow_date_filtering():
    """Test get_participants_with_cohort_starting_tomorrow with correct date filtering."""
    # Arrange & Act
    with patch.object(Cohort, "filter") as mock_cohort_filter:
        # Setup empty cohort result to focus on filter call verification
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_cohort_filter.return_value.prefetch_related.return_value = mock_prefetch

        await get_participants_with_cohort_starting_tomorrow()

        # Assert - Verify the filter was called with correct parameters
        mock_cohort_filter.assert_called_once()
        call_args = mock_cohort_filter.call_args[1]

        # Verify date range (should be tomorrow from frozen time)
        assert "started_at__gte" in call_args
        assert "started_at__lt" in call_args

        # The dates should be for tomorrow (2024-01-16)
        gte_date = call_args["started_at__gte"]
        lt_date = call_args["started_at__lt"]

        # Verify it's filtering for tomorrow's date range
        assert gte_date.date() == pendulum.parse("2024-01-16").date()
        assert lt_date.date() == pendulum.parse("2024-01-16").date()


@pytest.mark.asyncio
async def test_get_participants_with_cohort_starting_tomorrow_database_error():
    """Test get_participants_with_cohort_starting_tomorrow with database error."""
    # Arrange & Act & Assert
    with patch.object(Cohort, "filter", side_effect=Exception(DATABASE_ERROR_MSG)):
        with pytest.raises(Exception, match=DATABASE_ERROR_MSG):
            await get_participants_with_cohort_starting_tomorrow()


@pytest.mark.asyncio
async def test_get_participants_with_cohort_starting_tomorrow_multiple_cohorts():
    """Test get_participants_with_cohort_starting_tomorrow with multiple cohorts."""
    # Arrange
    mock_participant1 = MagicMock()
    mock_participant1.email = TEST_EMAIL

    mock_participant2 = MagicMock()
    mock_participant2.email = "<EMAIL>"

    mock_participant3 = MagicMock()
    mock_participant3.email = "<EMAIL>"

    mock_cohort1 = MagicMock()
    mock_cohort1.participants = [mock_participant1, mock_participant2]

    mock_cohort2 = MagicMock()
    mock_cohort2.participants = [mock_participant3]

    with patch.object(Cohort, "filter") as mock_cohort_filter:
        # Setup cohort filter chain
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_cohort1, mock_cohort2])
        mock_prefetch.all = mock_all
        mock_cohort_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_with_cohort_starting_tomorrow()

        # Assert
        assert len(result) == 3
        emails = [participant["email"] for participant in result]
        assert TEST_EMAIL in emails
        assert "<EMAIL>" in emails
        assert "<EMAIL>" in emails


# Tests for EmailHandler.send_cohort_starting_tomorrow_email method
def test_send_cohort_starting_tomorrow_email_success():
    """Test send_cohort_starting_tomorrow_email with successful email sending."""
    # Arrange
    test_emails = (TEST_EMAIL, "<EMAIL>")

    with patch.object(
        EmailHandler, "generate_message", return_value=MagicMock()
    ) as mock_generate:
        with patch.object(EmailHandler, "send_email") as mock_send:
            with patch(
                "ciba_participant.notifications.email.send_grid_email.settings"
            ) as mock_settings:
                mock_settings.DEV_EMAIL = "<EMAIL>"
                mock_settings.UI_HOST = "https://test.example.com"

                # Create EmailHandler instance
                email_handler = EmailHandler()

                # Act
                email_handler.send_cohort_starting_tomorrow_email(test_emails)

                # Assert
                mock_generate.assert_called_once()
                mock_send.assert_called_once()

                # Verify generate_message call parameters
                call_args = mock_generate.call_args
                assert call_args[1]["to_emails"] == test_emails
                assert call_args[1]["subject"] == COHORT_STARTING_TEMPLATE_SUBJECT
                assert call_args[1]["template_id"] == COHORT_STARTING_TEMPLATE_ID
                assert call_args[1]["bcc_emails_list"] == ["<EMAIL>"]

                # Verify dynamic template data
                template_data = call_args[1]["dynamic_template_data"]
                assert template_data["cta_link"] == "https://test.example.com/login"


def test_send_cohort_starting_tomorrow_email_empty_emails():
    """Test send_cohort_starting_tomorrow_email with empty email tuple."""
    # Arrange
    test_emails = ()

    with patch.object(
        EmailHandler, "generate_message", return_value=MagicMock()
    ) as mock_generate:
        with patch.object(EmailHandler, "send_email") as mock_send:
            with patch(
                "ciba_participant.notifications.email.send_grid_email.settings"
            ) as mock_settings:
                mock_settings.DEV_EMAIL = "<EMAIL>"
                mock_settings.UI_HOST = "https://test.example.com"

                # Create EmailHandler instance
                email_handler = EmailHandler()

                # Act
                email_handler.send_cohort_starting_tomorrow_email(test_emails)

                # Assert - Should still call generate and send with empty tuple
                mock_generate.assert_called_once()
                mock_send.assert_called_once()


def test_send_cohort_starting_tomorrow_email_send_error():
    """Test send_cohort_starting_tomorrow_email with email sending error."""
    # Arrange
    test_emails = (TEST_EMAIL,)

    with patch.object(
        EmailHandler, "generate_message", return_value=MagicMock()
    ) as mock_generate:
        with patch.object(
            EmailHandler, "send_email", side_effect=Exception("Send error")
        ) as mock_send:
            with patch(
                "ciba_participant.notifications.email.send_grid_email.settings"
            ) as mock_settings:
                mock_settings.DEV_EMAIL = "<EMAIL>"
                mock_settings.UI_HOST = "https://test.example.com"

                # Create EmailHandler instance
                email_handler = EmailHandler()

                # Act & Assert - Should raise exception
                with pytest.raises(Exception, match="Send error"):
                    email_handler.send_cohort_starting_tomorrow_email(test_emails)

                # Verify methods were called before error
                mock_generate.assert_called_once()
                mock_send.assert_called_once()


def test_send_cohort_starting_tomorrow_email_multiple_emails():
    """Test send_cohort_starting_tomorrow_email with multiple emails."""
    # Arrange
    test_emails = (TEST_EMAIL, "<EMAIL>", "<EMAIL>")

    with patch.object(
        EmailHandler, "generate_message", return_value=MagicMock()
    ) as mock_generate:
        with patch.object(EmailHandler, "send_email") as mock_send:
            with patch(
                "ciba_participant.notifications.email.send_grid_email.settings"
            ) as mock_settings:
                mock_settings.DEV_EMAIL = "<EMAIL>"
                mock_settings.UI_HOST = "https://test.example.com"

                # Create EmailHandler instance
                email_handler = EmailHandler()

                # Act
                email_handler.send_cohort_starting_tomorrow_email(test_emails)

                # Assert
                mock_generate.assert_called_once()
                mock_send.assert_called_once()

                # Verify generate_message was called with all emails
                call_args = mock_generate.call_args
                assert call_args[1]["to_emails"] == test_emails


# Additional tests for get_participants_with_class_in_24_hours function
@pytest.mark.asyncio
@freeze_time("2024-01-15 12:00:00")
async def test_get_participants_with_class_in_24_hours_time_window():
    """Test get_participants_with_class_in_24_hours with correct time window."""
    # Arrange
    with patch.object(Booking, "filter") as mock_booking_filter:
        # Setup empty booking result to focus on filter call verification
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[])

        mock_prefetch.all = mock_all
        mock_booking_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        await get_participants_with_class_in_24_hours()

        # Assert - Verify the filter was called with correct parameters
        mock_booking_filter.assert_called_once()
        call_args = mock_booking_filter.call_args[1]

        # Verify status filter
        assert call_args["status"] == BookingStatusEnum.BOOKED

        # Verify time window (should be 24 hours from frozen time with 30-minute window)
        assert "live_session__meeting_start_time__gte" in call_args
        assert "live_session__meeting_start_time__lt" in call_args

        # The start should be 24 hours from now
        start_time = call_args["live_session__meeting_start_time__gte"]
        end_time = call_args["live_session__meeting_start_time__lt"]

        # Verify it's 24 hours ahead with 30-minute window
        expected_start = pendulum.parse("2024-01-16T12:00:00Z")
        expected_end = expected_start.add(minutes=30)

        assert start_time == expected_start
        assert end_time == expected_end


@pytest.mark.asyncio
async def test_get_participants_with_class_in_24_hours_multiple_bookings():
    """Test get_participants_with_class_in_24_hours with multiple bookings."""
    # Arrange
    mock_participant1 = MagicMock()
    mock_participant1.email = "<EMAIL>"
    mock_participant1.first_name = "Test1"

    mock_participant2 = MagicMock()
    mock_participant2.email = "<EMAIL>"
    mock_participant2.first_name = "Test2"

    mock_session1 = MagicMock()
    mock_session1.title = "Class 1"
    mock_session1.meeting_start_time = TEST_MEETING_START_TIME

    mock_session2 = MagicMock()
    mock_session2.title = "Class 2"
    mock_session2.meeting_start_time = TEST_MEETING_START_TIME.add(minutes=15)

    mock_booking1 = MagicMock()
    mock_booking1.participant = mock_participant1
    mock_booking1.live_session = mock_session1

    mock_booking2 = MagicMock()
    mock_booking2.participant = mock_participant2
    mock_booking2.live_session = mock_session2

    with patch.object(Booking, "filter") as mock_booking_filter:
        # Setup the mock filter chain
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_booking1, mock_booking2])

        mock_prefetch.all = mock_all
        mock_booking_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_with_class_in_24_hours()

        # Assert
        assert len(result) == 2
        assert result[0]["email"] == "<EMAIL>"
        assert result[0]["first_name"] == "Test1"
        assert result[0]["class_name"] == "Class 1"
        assert result[1]["email"] == "<EMAIL>"
        assert result[1]["first_name"] == "Test2"
        assert result[1]["class_name"] == "Class 2"


@pytest.mark.asyncio
async def test_get_participants_with_class_in_24_hours_edge_case_times():
    """Test get_participants_with_class_in_24_hours with edge case times."""
    # Arrange
    mock_participant = MagicMock()
    mock_participant.email = TEST_EMAIL
    mock_participant.first_name = TEST_FIRST_NAME

    mock_session = MagicMock()
    mock_session.title = TEST_CLASS_TITLE
    mock_session.meeting_start_time = pendulum.parse(
        "2024-01-16T10:29:59Z"
    )  # Just within window

    mock_booking = MagicMock()
    mock_booking.participant = mock_participant
    mock_booking.live_session = mock_session

    with patch.object(Booking, "filter") as mock_booking_filter:
        # Setup the mock filter chain
        mock_prefetch = MagicMock()
        mock_all = AsyncMock(return_value=[mock_booking])

        mock_prefetch.all = mock_all
        mock_booking_filter.return_value.prefetch_related.return_value = mock_prefetch

        # Act
        result = await get_participants_with_class_in_24_hours()

        # Assert
        assert len(result) == 1
        assert result[0]["email"] == TEST_EMAIL
        assert result[0]["class_date"] == mock_session.meeting_start_time


# Additional comprehensive tests for EmailHandler.send_class_in_24_hours_reminder_email
class TestSendClassIn24HoursReminderEmailComprehensive:
    """Comprehensive test class for send_class_in_24_hours_reminder_email method."""

    def test_send_class_in_24_hours_reminder_email_basic_functionality(self):
        """Test basic functionality of send_class_in_24_hours_reminder_email."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = TEST_CLASS_TITLE
        class_date = TEST_MEETING_START_TIME

        with patch.object(
            EmailHandler, "generate_message", return_value=MagicMock()
        ) as mock_generate:
            with patch.object(EmailHandler, "send_email") as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act
                    result = handler.send_class_in_24_hours_reminder_email(
                        email, first_name, class_name, class_date
                    )

                    # Assert
                    assert result is True
                    mock_generate.assert_called_once()
                    mock_send.assert_called_once()

                    # Verify generate_message call parameters
                    call_args = mock_generate.call_args
                    assert call_args[1]["to_emails"] == email
                    assert call_args[1]["subject"] == CLASS_IN_24_HOURS_TEMPLATE_SUBJECT
                    assert call_args[1]["template_id"] == CLASS_IN_24_HOURS_TEMPLATE_ID
                    assert call_args[1]["bcc_emails_list"] == ["<EMAIL>"]

                    # Verify dynamic template data
                    template_data = call_args[1]["dynamic_template_data"]
                    assert template_data["first_name"] == first_name
                    assert template_data["class_name"] == class_name
                    assert "class_time" in template_data

    def test_send_class_in_24_hours_reminder_email_error_handling(self):
        """Test send_class_in_24_hours_reminder_email error handling."""
        # Arrange
        email = TEST_EMAIL
        first_name = TEST_FIRST_NAME
        class_name = TEST_CLASS_TITLE
        class_date = TEST_MEETING_START_TIME

        with patch.object(
            EmailHandler, "generate_message", side_effect=Exception("Generation error")
        ):
            with patch.object(EmailHandler, "send_email") as mock_send:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    handler = EmailHandler()

                    # Act & Assert
                    with pytest.raises(Exception, match="Generation error"):
                        handler.send_class_in_24_hours_reminder_email(
                            email, first_name, class_name, class_date
                        )

                    # Verify send_email was not called
                    mock_send.assert_not_called()


# Tests for send_class_recording_available_email method
class TestSendClassRecordingAvailableEmail:
    """Test class for send_class_recording_available_email method."""

    @pytest.fixture
    def mock_live_session_recording(self):
        """Create a mock live session for recording tests."""
        live_session = MagicMock()
        live_session.id = TestConstants.LIVE_SESSION_ID
        live_session.title = TestConstants.CLASS_TITLE
        live_session.meeting_start_time = TestConstants.MEETING_START_TIME
        return live_session

    @pytest.fixture
    def mock_booking_recording(self, mock_participant, mock_live_session_recording):
        """Create a mock booking for recording tests."""
        booking = MagicMock()
        booking.id = TestConstants.BOOKING_ID
        booking.participant = mock_participant
        booking.live_session = mock_live_session_recording
        booking.status = BookingStatusEnum.BOOKED
        return booking

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_success(
        self, mock_booking_recording
    ):
        """Test send_class_recording_available_email with successful email sending."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        async with setup_booking_filter_chain([mock_booking_recording]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert
                    assert_email_generation_call(
                        mocks["generate"],
                        TestConstants.EMAIL,
                        "Your AscendWell Class Recording Is Available!",
                        "d-1f9ef4facffc4134988666d1cb1a4304",  # CLASS_RECORDING_AVAILABLE_TEMPLATE_ID
                        {
                            "first_name": TestConstants.FIRST_NAME,
                            "class_name": TestConstants.CLASS_TITLE,
                            "class_date": "01/16/2024",
                            "class_time": "02:00 AM PST",
                            "cta_link": "https://test.example.com/classes/past",
                        },
                    )
                    mocks["send"].assert_called_once()

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_multiple_bookings(
        self, mock_participant, mock_live_session_recording
    ):
        """Test send_class_recording_available_email with multiple bookings."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        # Create multiple bookings
        booking1 = MagicMock()
        booking1.participant = mock_participant
        booking1.live_session = mock_live_session_recording
        booking1.status = BookingStatusEnum.BOOKED

        participant2 = MagicMock()
        participant2.email = "<EMAIL>"
        participant2.first_name = "Test2"

        booking2 = MagicMock()
        booking2.participant = participant2
        booking2.live_session = mock_live_session_recording
        booking2.status = BookingStatusEnum.BOOKED

        async with setup_booking_filter_chain([booking1, booking2]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert
                    assert mocks["generate"].call_count == 2
                    assert mocks["send"].call_count == 2

                    # Verify first email
                    assert_email_generation_call(
                        mocks["generate"],
                        TestConstants.EMAIL,
                        "Your AscendWell Class Recording Is Available!",
                        "d-1f9ef4facffc4134988666d1cb1a4304",
                        call_index=0,
                    )

                    # Verify second email
                    assert_email_generation_call(
                        mocks["generate"],
                        "<EMAIL>",
                        "Your AscendWell Class Recording Is Available!",
                        "d-1f9ef4facffc4134988666d1cb1a4304",
                        call_index=1,
                    )

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_canceled_booking_skipped(
        self, mock_participant, mock_live_session_recording
    ):
        """Test send_class_recording_available_email skips canceled bookings."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        # Create canceled booking
        canceled_booking = MagicMock()
        canceled_booking.participant = mock_participant
        canceled_booking.live_session = mock_live_session_recording
        canceled_booking.status = BookingStatusEnum.CANCELED

        # Create active booking
        active_booking = MagicMock()
        active_booking.participant = mock_participant
        active_booking.live_session = mock_live_session_recording
        active_booking.status = BookingStatusEnum.BOOKED

        async with setup_booking_filter_chain([canceled_booking, active_booking]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert - Only one email should be sent (for active booking)
                    assert mocks["generate"].call_count == 1
                    assert mocks["send"].call_count == 1

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_no_bookings(self):
        """Test send_class_recording_available_email with no bookings found."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        async with setup_booking_filter_chain([]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert - No emails should be sent
                    mocks["generate"].assert_not_called()
                    mocks["send"].assert_not_called()

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_database_error(self):
        """Test send_class_recording_available_email with database error."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        with patch.object(Booking, "filter", side_effect=Exception(DATABASE_ERROR_MSG)):
            # Create EmailHandler instance
            email_handler = EmailHandler()

            # Act & Assert
            with pytest.raises(Exception, match=DATABASE_ERROR_MSG):
                await email_handler.send_class_recording_available_email(
                    live_session_id
                )

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_send_error(
        self, mock_booking_recording
    ):
        """Test send_class_recording_available_email with email sending error."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        async with setup_booking_filter_chain([mock_booking_recording]):
            async with setup_email_handler_mocks() as mocks:
                # Override send to raise exception
                mocks["send"].side_effect = Exception("Email send error")

                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act & Assert
                    with pytest.raises(Exception, match="Email send error"):
                        await email_handler.send_class_recording_available_email(
                            live_session_id
                        )

                    # Verify methods were called before error
                    mocks["generate"].assert_called_once()
                    mocks["send"].assert_called_once()

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_date_time_formatting(
        self, mock_participant
    ):
        """Test send_class_recording_available_email with different date/time formats."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        # Create live session with different time
        live_session = MagicMock()
        live_session.id = TestConstants.LIVE_SESSION_ID
        live_session.title = "Evening Yoga Class"
        live_session.meeting_start_time = pendulum.parse(
            "2024-01-16T19:30:00-08:00"
        )  # 7:30 PM PT

        booking = MagicMock()
        booking.participant = mock_participant
        booking.live_session = live_session
        booking.status = BookingStatusEnum.BOOKED

        async with setup_booking_filter_chain([booking]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert
                    assert_email_generation_call(
                        mocks["generate"],
                        TestConstants.EMAIL,
                        "Your AscendWell Class Recording Is Available!",
                        "d-1f9ef4facffc4134988666d1cb1a4304",
                        {
                            "first_name": TestConstants.FIRST_NAME,
                            "class_name": "Evening Yoga Class",
                            "class_date": "01/16/2024",
                            "class_time": "07:30 PM PST",
                            "cta_link": "https://test.example.com/classes/past",
                        },
                    )
                    mocks["send"].assert_called_once()

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_template_data_verification(
        self, mock_booking_recording
    ):
        """Test send_class_recording_available_email template data is correctly formatted."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        async with setup_booking_filter_chain([mock_booking_recording]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://production.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert
                    mocks["generate"].assert_called_once()
                    call_args = mocks["generate"].call_args

                    # Verify all required parameters
                    assert call_args[1]["to_emails"] == TestConstants.EMAIL
                    assert (
                        call_args[1]["subject"]
                        == "Your AscendWell Class Recording Is Available!"
                    )
                    assert (
                        call_args[1]["template_id"]
                        == "d-1f9ef4facffc4134988666d1cb1a4304"
                    )
                    assert call_args[1]["content_type"] == "text/plain"
                    assert call_args[1]["bcc_emails_list"] == ["<EMAIL>"]

                    # Verify template data structure
                    template_data = call_args[1]["dynamic_template_data"]
                    assert "first_name" in template_data
                    assert "class_name" in template_data
                    assert "class_date" in template_data
                    assert "class_time" in template_data
                    assert "cta_link" in template_data
                    assert (
                        template_data["cta_link"]
                        == "https://production.example.com/classes/past"
                    )

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_mixed_booking_statuses(
        self, mock_participant, mock_live_session_recording
    ):
        """Test send_class_recording_available_email with mixed booking statuses."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        # Create bookings with different statuses
        booked_booking = MagicMock()
        booked_booking.participant = mock_participant
        booked_booking.live_session = mock_live_session_recording
        booked_booking.status = BookingStatusEnum.BOOKED

        canceled_booking = MagicMock()
        canceled_booking.participant = mock_participant
        canceled_booking.live_session = mock_live_session_recording
        canceled_booking.status = BookingStatusEnum.CANCELED

        # Create another participant with booked status
        participant2 = MagicMock()
        participant2.email = "<EMAIL>"
        participant2.first_name = "Participant2"

        booked_booking2 = MagicMock()
        booked_booking2.participant = participant2
        booked_booking2.live_session = mock_live_session_recording
        booked_booking2.status = BookingStatusEnum.BOOKED

        async with setup_booking_filter_chain(
            [booked_booking, canceled_booking, booked_booking2]
        ):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert - Only 2 emails should be sent (for booked bookings only)
                    assert mocks["generate"].call_count == 2
                    assert mocks["send"].call_count == 2

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_empty_live_session_id(self):
        """Test send_class_recording_available_email with empty live session ID."""
        # Arrange
        live_session_id = ""

        async with setup_booking_filter_chain([]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert - No emails should be sent
                    mocks["generate"].assert_not_called()
                    mocks["send"].assert_not_called()

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_logging_verification(
        self, mock_booking_recording, caplog
    ):
        """Test send_class_recording_available_email logs success messages."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        async with setup_booking_filter_chain([mock_booking_recording]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert
                    mocks["generate"].assert_called_once()
                    mocks["send"].assert_called_once()

                    # Verify logging
                    assert "Class recording available email sent to" in caplog.text
                    assert TestConstants.EMAIL in caplog.text
                    assert "successfully" in caplog.text

    @pytest.mark.asyncio
    async def test_send_class_recording_available_email_special_characters_in_class_name(
        self, mock_participant
    ):
        """Test send_class_recording_available_email with special characters in class name."""
        # Arrange
        live_session_id = str(TestConstants.LIVE_SESSION_ID)

        # Create live session with special characters in title
        live_session = MagicMock()
        live_session.id = TestConstants.LIVE_SESSION_ID
        live_session.title = "Mindful Eating & Wellness: A Journey to Health!"
        live_session.meeting_start_time = TestConstants.MEETING_START_TIME

        booking = MagicMock()
        booking.participant = mock_participant
        booking.live_session = live_session
        booking.status = BookingStatusEnum.BOOKED

        async with setup_booking_filter_chain([booking]):
            async with setup_email_handler_mocks() as mocks:
                with patch(
                    "ciba_participant.notifications.email.send_grid_email.settings"
                ) as mock_settings:
                    mock_settings.UI_HOST = "https://test.example.com"
                    mock_settings.DEV_EMAIL = "<EMAIL>"

                    # Create EmailHandler instance
                    email_handler = EmailHandler()

                    # Act
                    await email_handler.send_class_recording_available_email(
                        live_session_id
                    )

                    # Assert
                    assert_email_generation_call(
                        mocks["generate"],
                        TestConstants.EMAIL,
                        "Your AscendWell Class Recording Is Available!",
                        "d-1f9ef4facffc4134988666d1cb1a4304",
                        {
                            "first_name": TestConstants.FIRST_NAME,
                            "class_name": "Mindful Eating & Wellness: A Journey to Health!",
                            "class_date": "01/16/2024",
                            "class_time": "02:00 AM PST",
                            "cta_link": "https://test.example.com/classes/past",
                        },
                    )
                    mocks["send"].assert_called_once()
