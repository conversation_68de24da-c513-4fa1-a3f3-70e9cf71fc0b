import pytest
from unittest.mock import patch
from uuid import uuid4
from datetime import datetime

from ciba_participant.cohort.crud.get_paginated_cohorts import (
    Include,
    GetCohortsOutput,
    FilterInput,
    CohortStateFilter,
)
from ciba_participant.cohort.models import FullCohort
from ciba_participant.participant.models import RawParticipant


@pytest.fixture
def mock_repository():
    """Fixture to provide a patched CohortRepository with a mock get_paginated_cohorts method."""
    # Import the CohortRepository first
    from ciba_participant.cohort.crud import CohortRepository

    # Then patch the method directly
    with patch.object(
        CohortRepository, "get_paginated_cohorts"
    ) as mock_get_paginated_cohorts:
        yield CohortRepository, mock_get_paginated_cohorts


async def call_get_paginated_cohorts(
    repository, page=1, per_page=10, include=None, filters=None
):
    """Helper function to call get_paginated_cohorts with default parameters."""
    if include is None:
        include = {Include.participants, Include.program, Include.created_by}

    return await repository.get_paginated_cohorts(
        page=page, per_page=per_page, include=include, filters=filters
    )


def verify_mock_call(mock_process, page=1, per_page=10, include=None, filters=None):
    """Helper function to verify that the mock process was called with the correct arguments."""
    if include is None:
        include = {Include.participants, Include.program, Include.created_by}

    mock_process.assert_called_once_with(
        page=page, per_page=per_page, include=include, filters=filters
    )


@pytest.mark.asyncio
async def test_get_paginated_cohorts_with_empty_cohorts(mock_repository):
    """Test that get_paginated_cohorts handles empty cohorts correctly."""
    # Unpack the fixture
    repository, mock_process = mock_repository

    # Mock the process function to return an empty result
    mock_process.return_value = GetCohortsOutput(cohorts=[], total_pages=0)

    # Call the get_paginated_cohorts function
    result = await call_get_paginated_cohorts(repository)

    # Check the result
    assert isinstance(result, GetCohortsOutput)
    assert result.total_pages == 0
    assert len(result.cohorts) == 0

    # Check that the process function was called with the correct arguments
    verify_mock_call(mock_process)


@pytest.mark.asyncio
async def test_get_paginated_cohorts_with_filters(mock_repository):
    """Test that get_paginated_cohorts handles filters correctly."""
    # Unpack the fixture
    repository, mock_process = mock_repository

    # Mock the process function to return a result
    mock_process.return_value = GetCohortsOutput(cohorts=[], total_pages=0)

    # Create a filter
    filters = FilterInput(name_like="Test")

    # Call the get_paginated_cohorts function with filters
    result = await call_get_paginated_cohorts(
        repository, include={Include.participants}, filters=filters
    )

    # Check the result
    assert isinstance(result, GetCohortsOutput)

    # Check that the process function was called with the correct arguments
    verify_mock_call(mock_process, include={Include.participants}, filters=filters)


@pytest.mark.asyncio
async def test_get_paginated_cohorts_with_cohorts(mock_repository):
    """Test that get_paginated_cohorts handles cohorts correctly."""
    # Unpack the fixture
    repository, mock_process = mock_repository

    # Create a mock cohort
    cohort_id = uuid4()
    mock_cohort = FullCohort(
        id=cohort_id,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        name="Test Cohort",
        started_at=datetime.now(),
        limit=10,
        program_id=uuid4(),
        created_by_id=uuid4(),
        program=None,
        participants=[],
        created_by=None,
        program_modules=None,
    )

    # Mock the process function to return a result with the mock cohort
    mock_process.return_value = GetCohortsOutput(cohorts=[mock_cohort], total_pages=1)

    # Call the get_paginated_cohorts function
    result = await call_get_paginated_cohorts(
        repository, include={Include.participants}
    )

    # Check the result
    assert isinstance(result, GetCohortsOutput)
    assert result.total_pages == 1
    assert len(result.cohorts) == 1
    assert result.cohorts[0].id == cohort_id
    assert result.cohorts[0].name == "Test Cohort"

    # Check that the process function was called with the correct arguments
    verify_mock_call(mock_process, include={Include.participants})


@pytest.mark.asyncio
async def test_get_paginated_cohorts_with_participants(mock_repository):
    """Test that get_paginated_cohorts handles cohorts with participants correctly."""
    # Unpack the fixture
    repository, mock_process = mock_repository

    # Create a mock participant
    participant_id = uuid4()
    mock_participant = RawParticipant(
        id=participant_id,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid4(),
        member_id=uuid4(),
        status="active",
        cognito_sub=None,
        medical_record=None,
        is_test=False,
        last_reset=None,
        chat_identity="test_chat_identity",
    )

    # Create a mock cohort with the mock participant
    cohort_id = uuid4()
    mock_cohort = FullCohort(
        id=cohort_id,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        name="Test Cohort",
        started_at=datetime.now(),
        limit=10,
        program_id=uuid4(),
        created_by_id=uuid4(),
        program=None,
        participants=[mock_participant],
        created_by=None,
        program_modules=None,
    )

    # Mock the process function to return a result with the mock cohort
    mock_process.return_value = GetCohortsOutput(cohorts=[mock_cohort], total_pages=1)

    # Call the get_paginated_cohorts function
    result = await call_get_paginated_cohorts(
        repository, include={Include.participants}
    )

    # Check the result
    assert isinstance(result, GetCohortsOutput)
    assert result.total_pages == 1
    assert len(result.cohorts) == 1
    assert result.cohorts[0].id == cohort_id
    assert result.cohorts[0].name == "Test Cohort"
    assert len(result.cohorts[0].participants) == 1
    assert result.cohorts[0].participants[0].id == participant_id
    assert result.cohorts[0].participants[0].email == "<EMAIL>"

    # Check that the process function was called with the correct arguments
    verify_mock_call(mock_process, include={Include.participants})


@pytest.mark.asyncio
async def test_get_paginated_cohorts_with_pagination(mock_repository):
    """Test that get_paginated_cohorts handles pagination correctly."""
    # Unpack the fixture
    repository, mock_process = mock_repository

    # Mock the process function to return a result with pagination
    mock_process.return_value = GetCohortsOutput(cohorts=[], total_pages=5)

    # Call the get_paginated_cohorts function with pagination
    result = await call_get_paginated_cohorts(repository, page=2, include=set())

    # Check the result
    assert isinstance(result, GetCohortsOutput)
    assert result.total_pages == 5

    # Check that the process function was called with the correct arguments
    verify_mock_call(mock_process, page=2, include=set())


@pytest.mark.asyncio
async def test_get_paginated_cohorts_with_cohort_state_filter(mock_repository):
    """Test that get_paginated_cohorts handles cohort state filters correctly."""
    # Unpack the fixture
    repository, mock_process = mock_repository

    # Mock the process function to return a result
    mock_process.return_value = GetCohortsOutput(cohorts=[], total_pages=0)

    # Create a filter with cohort state
    filters = FilterInput(cohort_state=CohortStateFilter.empty)

    # Call the get_paginated_cohorts function with filters
    result = await call_get_paginated_cohorts(
        repository, include={Include.participants}, filters=filters
    )

    # Check the result
    assert isinstance(result, GetCohortsOutput)

    # Check that the process function was called with the correct arguments
    verify_mock_call(mock_process, include={Include.participants}, filters=filters)
