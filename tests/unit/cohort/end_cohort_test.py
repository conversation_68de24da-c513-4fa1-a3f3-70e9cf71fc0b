import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from contextlib import asynccontextmanager

import pytest
import pendulum

from ciba_participant.cohort.models import (
    Cohort,
    CohortMembers,
    CohortMembershipStatus,
    CohortStatusEnum,
)
from ciba_participant.participant.models import ParticipantStatus
from ciba_participant.cohort.crud.end_cohort import process, CohortNotEndedException


@asynccontextmanager
async def setup_end_cohort_mocks(
    cohort_result=None,
    participant_ids=None,
):
    """Set up all the mocks needed for end_cohort tests."""
    # Create unique patch objects for each test run
    cohort_filter_patch = patch.object(Cohort, "filter", autospec=True)
    cohort_members_filter_patch = patch.object(CohortMembers, "filter", autospec=True)
    transaction_patch = patch("ciba_participant.cohort.crud.end_cohort.in_transaction")
    logger_patch = patch("ciba_participant.cohort.crud.end_cohort.logger")
    participant_patch = patch("ciba_participant.cohort.crud.end_cohort.Participant")

    # Start all patches
    mock_cohort_filter = cohort_filter_patch.start()
    mock_cohort_members_filter = cohort_members_filter_patch.start()
    mock_transaction = transaction_patch.start()
    mock_logger = logger_patch.start()
    mock_participant = participant_patch.start()

    try:
        # Set up transaction context manager
        mock_transaction.return_value.__aenter__ = AsyncMock()
        mock_transaction.return_value.__aexit__ = AsyncMock()

        # Set up Cohort.filter() chain - create a single query object that handles both calls
        mock_cohort_query = MagicMock()
        mock_cohort_query.prefetch_related.return_value = mock_cohort_query
        mock_cohort_query.first = AsyncMock(return_value=cohort_result)

        # Set up update method
        mock_cohort_query.update = AsyncMock()

        # Always return the same query object for all Cohort.filter() calls
        mock_cohort_filter.return_value = mock_cohort_query

        # Set up CohortMembers.filter() chain
        mock_cohort_members_query = MagicMock()
        mock_cohort_members_query.values_list = AsyncMock(
            return_value=participant_ids or []
        )
        mock_cohort_members_filter.return_value = mock_cohort_members_query

        # Set up Participant.filter() chain
        mock_participant_query = MagicMock()
        mock_participant_query.update = AsyncMock()
        mock_participant.filter.return_value = mock_participant_query

        # Create results dictionary
        mocks = {
            "cohort_filter": mock_cohort_filter,
            "cohort_query": mock_cohort_query,
            "cohort_members_filter": mock_cohort_members_filter,
            "cohort_members_query": mock_cohort_members_query,
            "participant": mock_participant,
            "participant_query": mock_participant_query,
            "transaction": mock_transaction,
            "logger": mock_logger,
        }

        yield mocks
    finally:
        # Stop all patches explicitly to ensure cleanup
        cohort_filter_patch.stop()
        cohort_members_filter_patch.stop()
        transaction_patch.stop()
        logger_patch.stop()
        participant_patch.stop()


def create_mock_cohort(cohort_id=None, end_date_days_from_now=20):
    """Helper to create a mock cohort with a specific end date."""
    cohort = MagicMock()
    cohort.id = cohort_id or uuid.uuid4()
    cohort.name = f"test-cohort-{end_date_days_from_now}-days"

    # Create end_date as a property that returns a real pendulum object
    # The actual implementation uses `await cohort.end_date`, so we need to make it awaitable
    end_date = pendulum.now("UTC").add(days=end_date_days_from_now)

    # Create a coroutine that returns the end_date
    async def get_end_date():
        return end_date

    # Set end_date as a property that returns the coroutine
    cohort.end_date = get_end_date()

    return cohort


@pytest.fixture
def mock_cohort():
    """Create a mock cohort for testing (30 days from now - not near end)."""
    return create_mock_cohort(end_date_days_from_now=30)


@pytest.fixture
def mock_cohort_near_end():
    """Create a mock cohort that is near end for testing (20 days from now)."""
    return create_mock_cohort(end_date_days_from_now=20)


@pytest.fixture
def mock_participant_ids():
    """Create mock participant IDs for testing."""
    return [uuid.uuid4(), uuid.uuid4(), uuid.uuid4()]


@pytest.mark.asyncio
async def test_process_cohort_end_success(mock_cohort_near_end, mock_participant_ids):
    """Test successful cohort ending process."""
    # Arrange
    cohort_id = mock_cohort_near_end.id

    async with setup_end_cohort_mocks(
        cohort_result=mock_cohort_near_end,
        participant_ids=mock_participant_ids,
    ) as mocks:
        # Act
        result = await process(cohort_id)

        # Assert
        assert result is True

        # Verify cohort was retrieved correctly
        mocks["cohort_filter"].assert_called_with(id=cohort_id)
        mocks["cohort_query"].prefetch_related.assert_called_once_with(
            "program_modules"
        )
        mocks["cohort_query"].first.assert_called_once()

        # Verify cohort was updated to COMPLETED
        mocks["cohort_query"].update.assert_called_once_with(
            status=CohortStatusEnum.COMPLETED
        )

        # Verify participants were retrieved and updated
        mocks["cohort_members_filter"].assert_called_once_with(
            cohort_id=cohort_id, status=CohortMembershipStatus.ACTIVE
        )
        mocks["cohort_members_query"].values_list.assert_called_once_with(
            "participant_id", flat=True
        )
        mocks["participant"].filter.assert_called_once_with(
            id__in=mock_participant_ids, status=ParticipantStatus.ACTIVE
        )
        mocks["participant_query"].update.assert_called_once_with(
            status=ParticipantStatus.COMPLETED
        )


@pytest.mark.asyncio
async def test_process_cohort_end_success_no_participants(mock_cohort_near_end):
    """Test successful cohort ending process with no participants."""
    # Arrange
    cohort_id = mock_cohort_near_end.id
    empty_participant_ids = []

    async with setup_end_cohort_mocks(
        cohort_result=mock_cohort_near_end,
        participant_ids=empty_participant_ids,
    ) as mocks:
        # Act
        result = await process(cohort_id)

        # Assert
        assert result is True

        # Verify cohort was updated to COMPLETED
        mocks["cohort_query"].update.assert_called_once_with(
            status=CohortStatusEnum.COMPLETED
        )

        # Verify participants query was made but no update since list is empty
        mocks["cohort_members_query"].values_list.assert_called_once_with(
            "participant_id", flat=True
        )
        mocks["participant"].filter.assert_not_called()


@pytest.mark.asyncio
async def test_process_cohort_not_found():
    """Test processing when cohort is not found."""
    # Arrange
    cohort_id = uuid.uuid4()

    async with setup_end_cohort_mocks(
        cohort_result=None,
    ) as mocks:
        # Act & Assert
        with pytest.raises(ValueError, match=f"Cohort with ID {cohort_id} not found"):
            await process(cohort_id)

        # Verify cohort lookup was attempted
        mocks["cohort_filter"].assert_called_with(id=cohort_id)
        mocks["cohort_query"].first.assert_called_once()


@pytest.mark.asyncio
async def test_process_cohort_not_near_end(mock_cohort):
    """Test processing when cohort is not near end."""
    # Arrange
    cohort_id = mock_cohort.id

    async with setup_end_cohort_mocks(
        cohort_result=mock_cohort,
    ) as mocks:
        # Act & Assert
        with pytest.raises(CohortNotEndedException):
            await process(cohort_id)

        # Verify cohort was retrieved but processing stopped due to date check
        mocks["cohort_filter"].assert_called_with(id=cohort_id)
        mocks["cohort_query"].first.assert_called_once()
        # Should not proceed to update operations
        mocks["cohort_query"].update.assert_not_called()


@pytest.mark.asyncio
async def test_process_cohort_end_date_boundary_exactly_28_days():
    """Test cohort ending when end_date is exactly 28 days from now."""
    # Arrange
    cohort_id = uuid.uuid4()
    participant_ids = [uuid.uuid4()]

    # Create a cohort with end_date exactly 28 days from now
    cohort_28_days = create_mock_cohort(cohort_id=cohort_id, end_date_days_from_now=28)

    async with setup_end_cohort_mocks(
        cohort_result=cohort_28_days,
        participant_ids=participant_ids,
    ) as mocks:
        # Act
        result = await process(cohort_id)

        # Assert
        assert result is True

        # Verify cohort was updated to COMPLETED
        mocks["cohort_query"].update.assert_called_once_with(
            status=CohortStatusEnum.COMPLETED
        )

        # Verify participants were updated to COMPLETED
        mocks["participant"].filter.assert_called_once_with(
            id__in=participant_ids, status=ParticipantStatus.ACTIVE
        )
        mocks["participant_query"].update.assert_called_once_with(
            status=ParticipantStatus.COMPLETED
        )


@pytest.mark.asyncio
async def test_process_cohort_end_date_boundary_over_28_days():
    """Test cohort ending when end_date is over 28 days from now (should raise exception)."""
    # Arrange
    cohort_id = uuid.uuid4()

    # Create a cohort with end_date over 28 days from now
    cohort_29_days = create_mock_cohort(cohort_id=cohort_id, end_date_days_from_now=29)

    async with setup_end_cohort_mocks(
        cohort_result=cohort_29_days,
    ) as mocks:
        # Act & Assert
        with pytest.raises(CohortNotEndedException):
            await process(cohort_id)

        # Verify cohort was retrieved but processing stopped due to date check
        mocks["cohort_filter"].assert_called_with(id=cohort_id)
        mocks["cohort_query"].first.assert_called_once()
        # Should not proceed to update operations
        mocks["cohort_query"].update.assert_not_called()


@pytest.mark.asyncio
async def test_process_cohort_end_with_none_end_date():
    """Test cohort ending when end_date is None."""
    # Arrange
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.name = "test-cohort-no-end-date"

    # Mock the end_date property as an AsyncMock that returns None
    cohort.end_date = AsyncMock(return_value=None)

    cohort_id = cohort.id

    async with setup_end_cohort_mocks(
        cohort_result=cohort,
    ) as mocks:
        # Act & Assert
        # When end_date is None, the comparison with pendulum.now().add(days=28) should fail
        # This will likely raise a TypeError or similar exception
        with pytest.raises((TypeError, AttributeError)):
            await process(cohort_id)

        # Verify cohort was retrieved
        mocks["cohort_filter"].assert_called_with(id=cohort_id)
        mocks["cohort_query"].first.assert_called_once()


@pytest.mark.asyncio
async def test_cohort_not_ended_exception_inheritance():
    """Test that CohortNotEndedException is properly inherited from ValueError."""
    # Arrange & Act
    exception = CohortNotEndedException("Test message")

    # Assert
    assert isinstance(exception, ValueError)
    assert isinstance(exception, CohortNotEndedException)
