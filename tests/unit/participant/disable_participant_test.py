import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from contextlib import asynccontextmanager

import pytest

from ciba_participant.cohort.models import Cohort
from ciba_participant.participant.models import (
    ParticipantStatus,
    SoleraParticipant,
)

# Import at module level to ensure consistent patching
from ciba_participant.participant.crud import (
    ParticipantRepository,
    SoleraParticipantRepository,
)


@asynccontextmanager
async def setup_disable_participant_mocks(
    mock_participant=None, mock_cohort=None, find_participant=None
):
    """Set up all the mocks needed for disable_participant tests."""
    # Create unique patch objects for each test run
    find_patch = patch.object(
        ParticipantRepository, "_find_participant_by_email", new_callable=AsyncMock
    )
    delete_patch = patch.object(
        ParticipantRepository, "delete_participant", new_callable=AsyncMock
    )
    update_meta_patch = patch.object(
        ParticipantRepository,
        "_update_disenrollment_metadata",
        new_callable=AsyncMock,
    )
    handle_cohort_patch = patch.object(
        ParticipantRepository, "handle_cohort_removal", new_callable=AsyncMock
    )
    delete_bookings_patch = patch.object(
        ParticipantRepository,
        "_delete_participant_bookings",
        new_callable=AsyncMock,
    )
    disable_solera_patch = patch.object(
        SoleraParticipantRepository,
        "disable_solera_participant",
        new_callable=AsyncMock,
    )
    disable_cognito_patch = patch.object(
        ParticipantRepository, "_disable_cognito_user", new_callable=AsyncMock
    )
    send_notification_patch = patch.object(
        ParticipantRepository,
        "_send_disenrollment_notification",
        new_callable=AsyncMock,
    )

    # Start all patches
    mock_find = find_patch.start()
    mock_delete = delete_patch.start()
    mock_update_meta = update_meta_patch.start()
    mock_handle_cohort = handle_cohort_patch.start()
    mock_delete_bookings = delete_bookings_patch.start()
    mock_disable_solera = disable_solera_patch.start()
    mock_disable_cognito = disable_cognito_patch.start()
    mock_send_notification = send_notification_patch.start()

    try:
        # Set up returns
        if find_participant is not None:
            mock_find.return_value = find_participant

        if mock_cohort is not None:
            mock_handle_cohort.return_value = mock_cohort

        # Create results dictionary
        mocks = {
            "find": mock_find,
            "delete": mock_delete,
            "update_meta": mock_update_meta,
            "handle_cohort": mock_handle_cohort,
            "delete_bookings": mock_delete_bookings,
            "disable_solera": mock_disable_solera,
            "disable_cognito": mock_disable_cognito,
            "send_notification": mock_send_notification,
        }

        yield mocks
    finally:
        # Stop all patches explicitly to ensure cleanup
        find_patch.stop()
        delete_patch.stop()
        update_meta_patch.stop()
        handle_cohort_patch.stop()
        delete_bookings_patch.stop()
        disable_solera_patch.stop()
        disable_cognito_patch.stop()
        send_notification_patch.stop()


@asynccontextmanager
async def mock_solera_filter(return_value=None):
    """Mock SoleraParticipant.filter().get_or_none() chain."""
    with patch.object(
        SoleraParticipant,
        "filter",
        autospec=True,
    ) as mock_filter:
        mock_filter_obj = AsyncMock()
        mock_filter_obj.get_or_none = AsyncMock(return_value=return_value)
        mock_filter.return_value = mock_filter_obj

        yield mock_filter, mock_filter_obj


@asynccontextmanager
async def mock_cohort_filter(return_value=None):
    """Mock Cohort.filter().first() chain."""
    with patch.object(
        Cohort,
        "filter",
        autospec=True,
    ) as mock_filter:
        mock_filter_obj = AsyncMock()
        mock_filter_obj.first = AsyncMock(return_value=return_value)
        mock_filter.return_value = mock_filter_obj

        yield mock_filter, mock_filter_obj


@pytest.fixture
def mock_participant():
    """Create a mock participant for testing."""
    participant_id = uuid.uuid4()
    participant = MagicMock()
    participant.id = participant_id
    participant.email = "<EMAIL>"
    participant.status = ParticipantStatus.ACTIVE
    participant.save = AsyncMock()

    # Create a mock for participant_meta
    participant_meta = MagicMock()
    participant_meta.metadata = {"phone_number": "************", "enrolled": True}
    participant_meta.save = AsyncMock()
    participant.participant_meta = [participant_meta]

    return participant


@pytest.fixture
def mock_cohort():
    """Create a mock cohort for testing."""
    cohort = MagicMock()
    cohort.id = uuid.uuid4()
    cohort.unique_name = "test-cohort"
    return cohort


@pytest.mark.asyncio
async def test_disable_participant_by_email_success(mock_participant, mock_cohort):
    """Test successful participant disabling by email."""
    # Arrange
    email = "<EMAIL>"
    disenrolled_reason = "Test reason"

    # Use the existing context manager for mocks
    async with setup_disable_participant_mocks(
        mock_participant=mock_participant,
        mock_cohort=mock_cohort,
        find_participant=mock_participant,
    ) as mocks:
        result = await ParticipantRepository.disable_participant(
            email=email, disenrolledReason=disenrolled_reason
        )

        # Assert
        assert result is True
        mocks["find"].assert_called_once_with(email)
        mocks["delete"].assert_called_once_with(participant=mock_participant)
        mocks["update_meta"].assert_called_once_with(
            mock_participant, disenrolled_reason, None
        )
        mocks["handle_cohort"].assert_called_once_with(mock_participant, None)
        mocks["delete_bookings"].assert_called_once_with(mock_participant)
        mocks["disable_solera"].assert_called_once_with(mock_participant.id)
        mocks["disable_cognito"].assert_called_once_with(email)
        mocks["send_notification"].assert_called_once_with(mock_participant.id)


@pytest.mark.asyncio
async def test_disable_participant_by_object_success(mock_participant, mock_cohort):
    """Test successful participant disabling by participant object."""
    # Arrange
    disenrolled_reason = "Test reason"

    # Use the existing context manager for mocks
    async with setup_disable_participant_mocks(
        mock_participant=mock_participant, mock_cohort=mock_cohort
    ) as mocks:
        result = await ParticipantRepository.disable_participant(
            participant=mock_participant, disenrolledReason=disenrolled_reason
        )

        # Assert
        assert result is True
        mocks["delete"].assert_called_once_with(participant=mock_participant)
        mocks["update_meta"].assert_called_once_with(
            mock_participant, disenrolled_reason, None
        )
        mocks["handle_cohort"].assert_called_once_with(mock_participant, None)
        mocks["delete_bookings"].assert_called_once_with(mock_participant)
        mocks["disable_solera"].assert_called_once_with(mock_participant.id)
        mocks["disable_cognito"].assert_called_once_with(mock_participant.email)
        mocks["send_notification"].assert_called_once_with(mock_participant.id)


@pytest.mark.asyncio
async def test_disable_participant_with_cohort(mock_participant, mock_cohort):
    """Test disabling a participant with a provided cohort."""
    # Arrange
    disenrolled_reason = "Test reason"

    # Use the existing context manager for mocks
    async with setup_disable_participant_mocks(
        mock_participant=mock_participant, mock_cohort=mock_cohort
    ) as mocks:
        result = await ParticipantRepository.disable_participant(
            participant=mock_participant,
            cohort=mock_cohort,
            disenrolledReason=disenrolled_reason,
        )

        # Assert
        assert result is True
        mocks["delete"].assert_called_once_with(participant=mock_participant)
        mocks["update_meta"].assert_called_once_with(
            mock_participant, disenrolled_reason, None
        )
        mocks["handle_cohort"].assert_called_once_with(mock_participant, mock_cohort)
        mocks["delete_bookings"].assert_called_once_with(mock_participant)
        mocks["disable_solera"].assert_called_once_with(mock_participant.id)
        mocks["disable_cognito"].assert_called_once_with(mock_participant.email)
        mocks["send_notification"].assert_called_once_with(mock_participant.id)


@pytest.mark.asyncio
async def test_disable_participant_with_disenrollment_date(
    mock_participant, mock_cohort
):
    """Test disabling a participant with a specific disenrollment date."""
    # Arrange
    disenrolled_reason = "Test reason"
    disenrollment_date = "2025-01-01"

    # Use the existing context manager for mocks
    async with setup_disable_participant_mocks(
        mock_participant=mock_participant, mock_cohort=mock_cohort
    ) as mocks:
        result = await ParticipantRepository.disable_participant(
            participant=mock_participant,
            disenrolledReason=disenrolled_reason,
            disenrollmentDate=disenrollment_date,
        )

        # Assert
        assert result is True
        mocks["delete"].assert_called_once_with(participant=mock_participant)
        mocks["update_meta"].assert_called_once_with(
            mock_participant, disenrolled_reason, disenrollment_date
        )
        mocks["handle_cohort"].assert_called_once_with(mock_participant, None)
        mocks["delete_bookings"].assert_called_once_with(mock_participant)
        mocks["disable_solera"].assert_called_once_with(mock_participant.id)
        mocks["disable_cognito"].assert_called_once_with(mock_participant.email)
        mocks["send_notification"].assert_called_once_with(mock_participant.id)


@pytest.mark.asyncio
async def test_disable_participant_not_found():
    """Test disabling a participant that doesn't exist."""
    # Arrange
    email = "<EMAIL>"

    # Use a simpler patch for this test case
    with patch.object(
        ParticipantRepository,
        "_find_participant_by_email",
        new_callable=AsyncMock,
        return_value=None,
    ) as mock_find:
        # Act
        result = await ParticipantRepository.disable_participant(email=email)

        # Assert
        assert result is None
        mock_find.assert_called_once_with(email)


@pytest.mark.asyncio
async def test_disable_participant_exception(mock_participant):
    """Test handling of exceptions during participant disabling."""
    # Arrange
    email = "<EMAIL>"

    # Use the existing context manager with a custom setup
    async with setup_disable_participant_mocks(
        find_participant=mock_participant
    ) as mocks:
        # Override the delete_participant mock to raise an exception
        mocks["delete"].side_effect = Exception("Test exception")

        # Act & Assert
        with pytest.raises(Exception, match="Test exception"):
            await ParticipantRepository.disable_participant(email=email)

        mocks["find"].assert_called_once_with(email)
        mocks["delete"].assert_called_once_with(participant=mock_participant)


@pytest.mark.asyncio
async def test_update_disenrollment_metadata_with_solera(mock_participant):
    """Test updating disenrollment metadata with Solera participant."""
    # Arrange
    disenrolled_reason = "Test reason"
    disenrollment_date = "2025-01-01"

    # Create a mock Solera participant
    solera_participant = MagicMock()
    solera_participant.solera_program_id = "solera-123"

    # Use the existing context manager for solera filter
    async with mock_solera_filter(return_value=solera_participant):
        # Act
        await ParticipantRepository._update_disenrollment_metadata(
            mock_participant, disenrolled_reason, disenrollment_date
        )

        # Assert
        # Check that metadata was updated correctly
        expected_metadata = {
            "phone_number": "************",
            "enrolled": False,
            "disenrolledReason": disenrolled_reason,
            "disenrollmentDate": disenrollment_date,
            "solera_program_id": "solera-123",
        }
        assert mock_participant.participant_meta[0].metadata == expected_metadata
        mock_participant.participant_meta[0].save.assert_awaited_once()
        mock_participant.save.assert_awaited_once()


@pytest.mark.asyncio
async def test_update_disenrollment_metadata_without_solera(mock_participant):
    """Test updating disenrollment metadata without Solera participant."""
    # Arrange
    disenrolled_reason = "Test reason"
    disenrollment_date = None  # Test with None to trigger default date

    # Use the existing context manager for solera filter with None return value
    async with mock_solera_filter(return_value=None):
        # Mock pendulum.now
        with patch("ciba_participant.participant.crud.pendulum.now") as mock_now:
            mock_now.return_value.to_date_string.return_value = "2025-04-08"

            # Act
            await ParticipantRepository._update_disenrollment_metadata(
                mock_participant, disenrolled_reason, disenrollment_date
            )

            # Assert
            mock_now.assert_called_once_with("UTC")

            # Check that metadata was updated correctly
            expected_metadata = {
                "phone_number": "************",
                "enrolled": False,
                "disenrolledReason": disenrolled_reason,
                "disenrollmentDate": "2025-04-08",
                "solera_program_id": None,
            }
            assert mock_participant.participant_meta[0].metadata == expected_metadata
            mock_participant.participant_meta[0].save.assert_awaited_once()
            mock_participant.save.assert_awaited_once()


@pytest.mark.asyncio
async def test_update_disenrollment_metadata_no_metadata(mock_participant):
    """Test updating disenrollment metadata when participant has no metadata."""
    # Arrange
    disenrolled_reason = "Test reason"
    mock_participant.participant_meta = []  # Empty metadata

    # Act
    await ParticipantRepository._update_disenrollment_metadata(
        mock_participant, disenrolled_reason, None
    )

    # Assert - should log warning and return without error
    # No assertions needed as the function should just return


@pytest.mark.asyncio
async def test_handle_cohort_removal_with_cohort(mock_participant, mock_cohort):
    """Test handling cohort removal with provided cohort."""
    # Arrange

    # Create a patch for the handle_cohort_removal method
    with patch.object(
        ParticipantRepository, "handle_cohort_removal", new_callable=AsyncMock
    ) as mock_handle_cohort:
        # Configure the mock to return the cohort
        mock_handle_cohort.return_value = mock_cohort

        # Act
        result = await ParticipantRepository.handle_cohort_removal(
            mock_participant, mock_cohort
        )

        # Assert
        assert result == mock_cohort
        mock_handle_cohort.assert_called_once_with(mock_participant, mock_cohort)


@pytest.mark.asyncio
async def test_handle_cohort_removal_find_cohort(mock_participant, mock_cohort):
    """Test handling cohort removal when cohort needs to be found."""
    # Arrange

    # Create a patch for the handle_cohort_removal method
    with patch.object(
        ParticipantRepository, "handle_cohort_removal", new_callable=AsyncMock
    ) as mock_handle_cohort:
        # Configure the mock to return the cohort
        mock_handle_cohort.return_value = mock_cohort

        # Act
        result = await ParticipantRepository.handle_cohort_removal(
            mock_participant, None
        )

        # Assert
        assert result == mock_cohort
        mock_handle_cohort.assert_called_once_with(mock_participant, None)


@pytest.mark.asyncio
async def test_handle_cohort_removal_no_cohort(mock_participant):
    """Test handling cohort removal when no cohort is found."""
    # Arrange

    # Create a patch for the handle_cohort_removal method
    with patch.object(
        ParticipantRepository, "handle_cohort_removal", new_callable=AsyncMock
    ) as mock_handle_cohort:
        # Configure the mock to return None
        mock_handle_cohort.return_value = None

        # Act
        result = await ParticipantRepository.handle_cohort_removal(
            mock_participant, None
        )

        # Assert
        assert result is None
        mock_handle_cohort.assert_called_once_with(mock_participant, None)
