import datetime
import json
import uuid
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from strawberry.types import Info

from ciba_participant.common.aws_handler import (
    EmailNotificationEvent,
)
from ciba_participant.participant.models import Participant, ParticipantStatus
from ciba_participant.participant.service import (
    ParticipantService,
)
from ciba_participant.common.cognito import admin_create_user


@pytest.fixture(scope="function")
def mock_settings():
    with patch("ciba_participant.participant.service.settings") as mock_settings:
        mock_settings.SECRET_KEY = "test_secret_key"
        mock_settings.PARTICIPANT_EMAIL_SQS_URL = "test_sqs_url"
        mock_settings.ENV = "TEST"
        yield mock_settings


@pytest.fixture(scope="function")
def mock_info():
    mock_info = MagicMock(spec=Info)
    mock_info.context.request.headers = {"X-Request-ID": "test-correlation-id"}
    return mock_info


@pytest.fixture(scope="function")
def mock_send_to_sqs():
    with patch("ciba_participant.participant.service.send_to_sqs") as mock:
        yield mock


@pytest.mark.asyncio
async def test_verify_change_password_token_invalid(mock_info):
    # Mock decode_activation_token to return empty dict (invalid token)
    with patch(
        "ciba_participant.participant.service.ParticipantService.decode_activation_token"
    ) as mock_decode:
        from ciba_participant.participant.service import ParticipantService

        participant_service = ParticipantService()

        mock_decode.return_value = {}

        # Call the method
        result = await participant_service.verify_change_password_token(
            "invalid_token", mock_info
        )

        # Assertions
        assert result == {"InvalidVerifyChangePasswordToken": "Invalid token"}
        mock_decode.assert_called_once_with("invalid_token")


@pytest.mark.asyncio
async def test_verify_change_password_token_expired(mock_info):
    # Create a test participant with a last_reset time
    last_reset = datetime.datetime.now(datetime.timezone.utc)
    test_participant = Participant(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        is_test=True,
        last_reset=last_reset,
    )

    # Create a token with an older last_reset time
    old_time = last_reset - datetime.timedelta(hours=1)
    token_data = {
        "email": "<EMAIL>",
        "tmp_password": "TestPassword123!",
        "last_reset": old_time.isoformat(),
    }

    # Mock decode_activation_token
    with patch(
        "ciba_participant.participant.service.ParticipantService.decode_activation_token"
    ) as mock_decode:
        mock_decode.return_value = token_data

        # Mock Participant.filter().get()
        with patch(
            "ciba_participant.participant.service.Participant.filter"
        ) as mock_filter:
            from ciba_participant.participant.service import ParticipantService

            participant_service = ParticipantService()

            mock_filter_instance = AsyncMock()
            mock_filter_instance.get.return_value = test_participant
            mock_filter.return_value = mock_filter_instance

            # Call the method
            result = await participant_service.verify_change_password_token(
                "test_token", mock_info
            )

            # Assertions
            assert result == {"VerifyChangePasswordTokenExpired": "Token expired"}
            mock_decode.assert_called_once_with("test_token")
            mock_filter.assert_called_once_with(email="<EMAIL>")
            mock_filter_instance.get.assert_called_once()


@pytest.mark.asyncio
async def test_forgot_password():
    # Create a test participant
    test_participant = Participant(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        is_test=True,
    )

    # Mock Participant.filter().first()
    with patch(
        "ciba_participant.participant.service.Participant.filter"
    ) as mock_filter:
        mock_filter_instance = AsyncMock()
        mock_filter_instance.first.return_value = test_participant
        mock_filter.return_value = mock_filter_instance

        # Mock invoke_cognito
        with patch(
            "ciba_participant.participant.service.ParticipantService.invoke_cognito",
            new_callable=MagicMock,
        ) as mock_invoke_cognito:
            from ciba_participant.participant.service import ParticipantService

            participant_service = ParticipantService()

            mock_invoke_cognito.return_value = {}

            # Call the method
            result = await participant_service.forgot_password("<EMAIL>")

            # Assertions
            assert result is True
            mock_filter.assert_called_once_with(email="<EMAIL>")
            mock_filter_instance.first.assert_called_once()
            mock_invoke_cognito.assert_called_once()


@pytest.mark.asyncio
async def test_update_status_active(mock_info, mock_send_to_sqs):
    # Create a test participant
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.PENDING,
        is_test=True,
        cognito_sub=None,
    )

    # Mock Participant.filter().get_or_none()
    with patch(
        "ciba_participant.participant.service.Participant.filter"
    ) as mock_filter:
        from ciba_participant.participant.service import ParticipantService

        participant_service = ParticipantService()

        # Update participant_service
        participant_service.participant_id = test_uuid
        participant_service.status = ParticipantStatus.ACTIVE.value

        mock_filter_instance = AsyncMock()
        mock_filter_instance.get_or_none.return_value = test_participant
        mock_filter.return_value = mock_filter_instance

        # Mock _on_active
        with patch.object(participant_service, "_on_active") as mock_on_active:
            mock_on_active.return_value = None

            # Mock participant.save()
            with patch.object(test_participant, "save") as mock_save:
                mock_save.return_value = None

                # Call the method
                result = await participant_service.update_status(mock_info)

                # Assertions
                assert result is test_participant
                assert result.status == ParticipantStatus.ACTIVE.value
                mock_filter.assert_called_once_with(id=test_uuid)
                mock_filter_instance.get_or_none.assert_called_once()
                mock_on_active.assert_called_once_with(mock_info, test_participant)
                mock_save.assert_called_once()


@pytest.mark.asyncio
async def test_update_status_rejected(mock_info):
    # Create a test participant
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.PENDING,
        is_test=True,
    )

    # Mock Participant.filter().get_or_none()
    with patch(
        "ciba_participant.participant.service.Participant.filter"
    ) as mock_filter:
        mock_filter_instance = AsyncMock()
        mock_filter_instance.get_or_none.return_value = test_participant
        mock_filter.return_value = mock_filter_instance

        from ciba_participant.participant.service import ParticipantService

        participant_service = ParticipantService()

        # Update participant_service
        participant_service.participant_id = test_uuid
        participant_service.status = ParticipantStatus.REJECTED.value

        # Mock _on_reject
        with patch.object(participant_service, "_on_reject") as mock_on_reject:
            mock_on_reject.return_value = None

            # Mock participant.save()
            with patch.object(test_participant, "save") as mock_save:
                mock_save.return_value = None

                # Call the method
                result = await participant_service.update_status(mock_info)

                # Assertions
                assert result is test_participant
                assert result.status == ParticipantStatus.REJECTED.value
                mock_filter.assert_called_once_with(id=test_uuid)
                mock_filter_instance.get_or_none.assert_called_once()
                mock_on_reject.assert_called_once_with(mock_info, test_participant)
                mock_save.assert_called_once()


@pytest.mark.asyncio
async def test_update_email_success():
    # Create a test participant
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        is_test=True,
    )

    # Mock Participant.filter().get_or_none()
    with patch(
        "ciba_participant.participant.service.Participant.filter"
    ) as mock_filter:
        mock_filter_instance = AsyncMock()
        mock_filter_instance.get_or_none.return_value = test_participant
        mock_filter.return_value = mock_filter_instance

        # Mock invoke_cognito
        with patch(
            "ciba_participant.participant.service.ParticipantService.invoke_cognito",
            new_callable=MagicMock,
        ) as mock_invoke_cognito:
            mock_invoke_cognito.return_value = {}

            # Mock participant.save()
            with patch.object(test_participant, "save") as mock_save:
                from ciba_participant.participant.service import ParticipantService

                participant_service = ParticipantService()

                # Update participant_service
                participant_service.participant_id = test_uuid
                participant_service.email = "<EMAIL>"

                # Call the method
                result = await participant_service.update_email()

                # Assertions
                assert result is True
                assert test_participant.email == "<EMAIL>"
                mock_filter.assert_called_once_with(id=test_uuid)
                mock_filter_instance.get_or_none.assert_called_once()
                assert mock_invoke_cognito.call_count == 2
                mock_save.assert_called_once()


@pytest.mark.asyncio
async def test_update_email_no_participant():
    # Mock Participant.filter().get_or_none() to return None
    with patch(
        "ciba_participant.participant.service.Participant.filter"
    ) as mock_filter:
        from ciba_participant.participant.service import ParticipantService

        participant_service = ParticipantService()

        # Update participant_service
        participant_service.participant_id = uuid.uuid4()
        participant_service.email = "<EMAIL>"

        mock_filter_instance = AsyncMock()
        mock_filter_instance.get_or_none.return_value = None
        mock_filter.return_value = mock_filter_instance

        # Call the method
        result = await participant_service.update_email()

        # Assertions
        assert result is False
        mock_filter.assert_called_once_with(id=participant_service.participant_id)
        mock_filter_instance.get_or_none.assert_called_once()


@pytest.mark.asyncio
async def test_set_new_password(mock_info, mock_send_to_sqs, mock_settings):
    # Create a test participant
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.PENDING,
        is_test=True,
    )

    # Mock Participant.filter().prefetch_related().first()
    with patch(
        "ciba_participant.participant.service.Participant.filter"
    ) as mock_filter:
        # Create a mock for the entire chain
        mock_chain = AsyncMock()
        mock_chain.first.return_value = test_participant

        # Set up the filter mock to return a mock with prefetch_related method
        mock_filter.return_value.prefetch_related.return_value = mock_chain

        # Mock invoke_cognito
        with patch(
            "ciba_participant.participant.service.ParticipantService.invoke_cognito",
            new_callable=MagicMock,
        ) as mock_invoke_cognito:
            mock_invoke_cognito.return_value = {}

            # Mock ParticipantActivity.save() directly
            with patch(
                "ciba_participant.participant.service.ParticipantActivity.save",
                new_callable=AsyncMock,
            ) as mock_activity_save:
                # Mock participant.save()
                with patch.object(test_participant, "save") as mock_save:
                    from ciba_participant.participant.service import ParticipantService

                    participant_service = ParticipantService()

                    mock_save.return_value = None

                    # Call the method
                    result = await participant_service.set_new_password(
                        mock_info, "<EMAIL>", "NewPassword123!"
                    )

                    # Assertions
                    assert result is test_participant
                    assert result.status == ParticipantStatus.ACTIVE.value
                    assert result.cognito_sub == test_uuid
                    mock_filter.assert_called_once_with(email="<EMAIL>")
                    # mock_filter_instance.prefetch_related.assert_called_once_with("participant_meta")
                    mock_filter.return_value.prefetch_related.assert_called_once_with(
                        "participant_meta"
                    )

                    mock_invoke_cognito.assert_called_once()
                    mock_activity_save.assert_called_once()
                    mock_save.assert_called_once()
                    assert mock_send_to_sqs.call_count == 1


@pytest.mark.asyncio
async def test_set_new_password_no_participant(mock_info):
    # Mock Participant.filter().prefetch_related().first() to return None
    with patch(
        "ciba_participant.participant.service.Participant.filter"
    ) as mock_filter:
        from ciba_participant.participant.service import ParticipantService

        participant_service = ParticipantService()

        # Create a mock for the entire chain
        mock_chain = AsyncMock()
        mock_chain.first.return_value = None

        # Set up the filter mock to return a mock with prefetch_related method
        mock_filter.return_value.prefetch_related.return_value = mock_chain

        # Call the method
        result = await participant_service.set_new_password(
            mock_info, "<EMAIL>", "NewPassword123!"
        )

        # Assertions
        assert result is None
        mock_filter.assert_called_once_with(email="<EMAIL>")
        mock_filter.return_value.prefetch_related.assert_called_once_with(
            "participant_meta"
        )


@pytest.mark.asyncio
async def test_on_active_new_user(mock_info, mock_send_to_sqs):
    # Create a test participant without cognito_sub
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.PENDING,
        is_test=True,
        cognito_sub=None,
    )

    # Mock generate_random_password
    with patch(
        "ciba_participant.participant.service.generate_random_password"
    ) as mock_generate_password:
        mock_generate_password.return_value = "TestPassword123!"

        # Mock invoke_cognito
        with patch(
            "ciba_participant.participant.service.ParticipantService.invoke_cognito",
            new_callable=MagicMock,
        ) as mock_invoke_cognito:
            mock_invoke_cognito.return_value = {}

            # Mock get_totp
            with patch(
                "ciba_participant.participant.service.ParticipantService.get_totp"
            ) as mock_get_totp:
                from ciba_participant.participant.service import ParticipantService

                participant_service = ParticipantService()

                mock_get_totp.return_value = "test_totp_token"

                # Call the method
                await participant_service._on_active(mock_info, test_participant)

                # Assertions
                assert test_participant.cognito_sub == test_uuid
                mock_generate_password.assert_called_once()
                assert mock_invoke_cognito.call_count == 2
                mock_get_totp.assert_called_once_with(
                    test_participant, "TestPassword123!"
                )
                mock_send_to_sqs.assert_called_once()

                # Verify SQS notification
                notification_call = mock_send_to_sqs.call_args[1]
                notification_data = json.loads(notification_call["message_body"])
                assert (
                    notification_data["email_event"]
                    == EmailNotificationEvent.CONFIRM_BY_PARTICIPANT.value
                )
                assert notification_data["data"]["email"] == "<EMAIL>"
                assert notification_data["data"]["first_name"] == "Test"
                assert notification_data["data"]["tmp_password"] == "test_totp_token"


@pytest.mark.asyncio
async def test_on_active_existing_user(mock_info, mock_send_to_sqs):
    # Create a test participant with cognito_sub
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.PENDING,
        is_test=True,
        cognito_sub=test_uuid,
    )

    # Mock invoke_cognito
    with patch(
        "ciba_participant.participant.service.ParticipantService.invoke_cognito",
        new_callable=MagicMock,
    ) as mock_invoke_cognito:
        from ciba_participant.participant.service import ParticipantService

        participant_service = ParticipantService()

        mock_invoke_cognito.return_value = {}

        # Call the method
        await participant_service._on_active(mock_info, test_participant)

        # Assertions
        mock_invoke_cognito.assert_called_once()
        mock_send_to_sqs.assert_called_once()

        # Verify SQS notification
        notification_call = mock_send_to_sqs.call_args[1]
        notification_data = json.loads(notification_call["message_body"])
        assert (
            notification_data["email_event"]
            == EmailNotificationEvent.ACTIVATE_PARTICIPANT.value
        )
        assert notification_data["data"]["email"] == "<EMAIL>"
        assert notification_data["data"]["first_name"] == "Test"


def test_on_reject_with_cognito_sub(mock_info, mock_send_to_sqs):
    # Create a test participant with cognito_sub
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.ACTIVE,
        is_test=True,
        cognito_sub=test_uuid,
    )

    # Mock invoke_cognito
    with patch(
        "ciba_participant.participant.service.ParticipantService.invoke_cognito",
        new_callable=MagicMock,
    ) as mock_invoke_cognito:
        from ciba_participant.participant.service import ParticipantService

        participant_service = ParticipantService()

        mock_invoke_cognito.return_value = {}

        # Call the method
        participant_service._on_reject(mock_info, test_participant)

        # Assertions
        mock_invoke_cognito.assert_called_once()
        mock_send_to_sqs.assert_called_once()

        # Verify SQS notification
        notification_call = mock_send_to_sqs.call_args[1]
        notification_data = json.loads(notification_call["message_body"])
        assert (
            notification_data["email_event"]
            == EmailNotificationEvent.REJECT_PARTICIPANT.value
        )
        assert notification_data["data"]["email"] == "<EMAIL>"
        assert notification_data["data"]["first_name"] == "Test"


def test_on_reject_without_cognito_sub(mock_info, mock_send_to_sqs):
    # Create a test participant without cognito_sub
    from ciba_participant.participant.service import ParticipantService

    participant_service = ParticipantService()

    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.ACTIVE,
        is_test=True,
        cognito_sub=None,
    )

    # Call the method
    participant_service._on_reject(mock_info, test_participant)

    # Assertions
    mock_send_to_sqs.assert_called_once()

    # Verify SQS notification
    notification_call = mock_send_to_sqs.call_args[1]
    notification_data = json.loads(notification_call["message_body"])
    assert (
        notification_data["email_event"]
        == EmailNotificationEvent.REJECT_PARTICIPANT.value
    )
    assert notification_data["data"]["email"] == "<EMAIL>"
    assert notification_data["data"]["first_name"] == "Test"


def test_on_pending_with_cognito_sub():
    # Create a test participant with cognito_sub
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.ACTIVE,
        is_test=True,
        cognito_sub=test_uuid,
    )

    # Mock invoke_cognito
    with patch(
        "ciba_participant.participant.service.ParticipantService.invoke_cognito",
        new_callable=MagicMock,
    ) as mock_invoke_cognito:
        from ciba_participant.participant.service import ParticipantService

        participant_service = ParticipantService()

        mock_invoke_cognito.return_value = {}

        # Call the method
        participant_service._on_pending(test_participant)

        # Assertions
        mock_invoke_cognito.assert_called_once()


def test_on_pending_without_cognito_sub():
    from ciba_participant.participant.service import ParticipantService

    participant_service = ParticipantService()

    # Create a test participant without cognito_sub
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.ACTIVE,
        is_test=True,
        cognito_sub=None,
    )

    # Call the method
    participant_service._on_pending(test_participant)

    # No assertions needed as the method should do nothing in this case


def test_get_totp(mock_settings):
    # Create a test participant
    test_uuid = uuid.uuid4()
    test_participant = Participant(
        id=test_uuid,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        group_id=uuid.uuid4(),
        member_id=uuid.uuid4(),
        status=ParticipantStatus.ACTIVE,
        is_test=True,
        cognito_sub=test_uuid,
        last_reset=datetime.datetime.now(datetime.timezone.utc),
    )

    # Call the method
    result = ParticipantService.get_totp(test_participant, "TestPassword123!")

    # Assertions
    assert isinstance(result, str)
    assert len(result) > 0


def test_invoke_cognito_local_env(mock_settings):
    # Set ENV to LOCAL
    from ciba_participant.participant.service import ParticipantService

    participant_service = ParticipantService()

    mock_settings.ENV = "local"

    # Call the method
    result = participant_service.invoke_cognito(admin_create_user, "<EMAIL>")

    # Assertions
    assert "User" in result
    assert "Username" in result["User"]
    assert "UserStatus" in result
    assert result["UserStatus"] == "CONFIRMED"


def test_invoke_cognito_prod_env(mock_settings):
    # Set ENV to PROD
    from ciba_participant.participant.service import ParticipantService

    participant_service = ParticipantService()

    mock_settings.ENV = "prod"

    # Mock the cognito function
    mock_cognito_func = MagicMock()
    mock_cognito_func.return_value = {"test": "result"}

    # Call the method
    result = participant_service.invoke_cognito(mock_cognito_func, "<EMAIL>")

    # Assertions
    assert result == {"test": "result"}
    mock_cognito_func.assert_called_once_with("<EMAIL>")
