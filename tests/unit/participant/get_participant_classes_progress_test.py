import uuid
from datetime import datetime, timezone
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from contextlib import asynccontextmanager

import pytest

from ciba_participant.classes.models import (
    Booking,
    RawLiveSession,
    RawBooking,
    RawWebinar,
)
from ciba_participant.participant.models import RawParticipant, RawAuthorized
from ciba_participant.participant.pydantic_models import ParticipantClassProgress
from ciba_participant.participant.crud import ParticipantRepository


@asynccontextmanager
async def setup_get_participant_classes_progress_mocks(
    mock_bookings=None, mock_query_result=None
):
    """Set up all the mocks needed for get_participant_classes_progress tests."""
    # Create patch for Booking.filter
    booking_filter_patch = patch.object(Booking, "filter", new_callable=MagicMock)

    # Create patch for RawLiveSession.model_validate
    raw_live_session_patch = patch.object(
        RawLiveSession, "model_validate", new_callable=MagicMock
    )

    # Create patch for RawParticipant.model_validate
    raw_participant_patch = patch.object(
        RawParticipant, "model_validate", new_callable=MagicMock
    )

    # Create patch for RawBooking.model_validate
    raw_booking_patch = patch.object(
        RawBooking, "model_validate", new_callable=MagicMock
    )

    # Create patch for RawAuthorized.model_validate
    raw_authorized_patch = patch.object(
        RawAuthorized, "model_validate", new_callable=MagicMock
    )

    # Create patch for RawWebinar.model_validate
    raw_webinar_patch = patch.object(
        RawWebinar, "model_validate", new_callable=MagicMock
    )

    # Start all patches
    mock_booking_filter = booking_filter_patch.start()
    mock_raw_live_session = raw_live_session_patch.start()
    mock_raw_participant = raw_participant_patch.start()
    mock_raw_booking = raw_booking_patch.start()
    mock_raw_authorized = raw_authorized_patch.start()
    mock_raw_webinar = raw_webinar_patch.start()

    try:
        # Set up the query chain mock
        mock_query = MagicMock()
        mock_query.filter.return_value = mock_query
        mock_query.prefetch_related.return_value = mock_query
        mock_query.all.return_value = AsyncMock()
        mock_query.all.return_value.order_by = AsyncMock(
            return_value=mock_bookings if mock_bookings is not None else []
        )

        mock_booking_filter.return_value = mock_query

        # Set up model validation returns
        if mock_query_result:
            mock_raw_live_session.return_value = mock_query_result.get("live_session")
            mock_raw_participant.return_value = mock_query_result.get("participant")
            mock_raw_booking.return_value = mock_query_result.get("booking")
            mock_raw_authorized.return_value = mock_query_result.get("host")
            mock_raw_webinar.return_value = mock_query_result.get("webinar")

        # Create results dictionary
        mocks = {
            "booking_filter": mock_booking_filter,
            "query": mock_query,
            "raw_live_session": mock_raw_live_session,
            "raw_participant": mock_raw_participant,
            "raw_booking": mock_raw_booking,
            "raw_authorized": mock_raw_authorized,
            "raw_webinar": mock_raw_webinar,
        }

        yield mocks
    finally:
        # Stop all patches explicitly to ensure cleanup
        booking_filter_patch.stop()
        raw_live_session_patch.stop()
        raw_participant_patch.stop()
        raw_booking_patch.stop()
        raw_authorized_patch.stop()
        raw_webinar_patch.stop()


@pytest.fixture
def mock_participant_id():
    """Create a mock participant ID for testing."""
    return uuid.uuid4()


@pytest.fixture
def mock_booking():
    """Create a mock booking for testing."""
    booking = MagicMock()
    booking.id = uuid.uuid4()
    booking.participant_id = uuid.uuid4()
    booking.live_session_id = uuid.uuid4()
    booking.created_at = datetime.now(timezone.utc)
    booking.updated_at = datetime.now(timezone.utc)

    # Mock live_session
    booking.live_session = MagicMock()
    booking.live_session.id = booking.live_session_id
    booking.live_session.title = "Test Live Session"
    booking.live_session.meeting_start_time = datetime.now(timezone.utc)

    # Mock participant
    booking.participant = MagicMock()
    booking.participant.id = booking.participant_id
    booking.participant.email = "<EMAIL>"
    booking.participant.first_name = "Test"
    booking.participant.last_name = "User"

    # Mock webinar and host
    booking.live_session.webinar = MagicMock()
    booking.live_session.webinar.id = uuid.uuid4()
    booking.live_session.webinar.title = "Test Webinar"
    booking.live_session.webinar.description = "Test Webinar Description"

    booking.live_session.webinar.host = MagicMock()
    booking.live_session.webinar.host.id = uuid.uuid4()
    booking.live_session.webinar.host.email = "<EMAIL>"
    booking.live_session.webinar.host.first_name = "Host"
    booking.live_session.webinar.host.last_name = "User"

    return booking


@pytest.fixture
def mock_raw_models():
    """Create mock raw model instances for testing."""
    return {
        "live_session": MagicMock(spec=RawLiveSession),
        "participant": MagicMock(spec=RawParticipant),
        "booking": MagicMock(spec=RawBooking),
        "host": MagicMock(spec=RawAuthorized),
        "webinar": MagicMock(spec=RawWebinar),
    }


class TestGetParticipantClassesProgress:
    """Test suite for the get_participant_classes_progress function."""

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_no_date_filters(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test fetching participant class progress without date filters."""
        # Arrange
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1
            assert isinstance(result[0], ParticipantClassProgress)

            # Verify the query was built correctly
            mocks["booking_filter"].assert_called_once_with(
                participant_id=mock_participant_id
            )
            mocks["query"].prefetch_related.assert_called_once_with(
                "live_session__webinar__host", "participant"
            )
            mocks["query"].all.assert_called_once()

            # Verify model validation was called
            mocks["raw_live_session"].assert_called_once_with(mock_booking.live_session)
            mocks["raw_participant"].assert_called_once_with(mock_booking.participant)
            mocks["raw_booking"].assert_called_once_with(mock_booking)
            mocks["raw_authorized"].assert_called_once_with(
                mock_booking.live_session.webinar.host
            )
            mocks["raw_webinar"].assert_called_once_with(
                mock_booking.live_session.webinar
            )

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_with_start_date(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test fetching participant class progress with start date filter."""
        # Arrange
        start_date = datetime(2025, 1, 1, tzinfo=timezone.utc)
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id, start_date=start_date
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify the query was built correctly with start date filter
            mocks["booking_filter"].assert_called_once_with(
                participant_id=mock_participant_id
            )
            # Verify filter was called for start date
            filter_calls = mocks["query"].filter.call_args_list
            assert len(filter_calls) == 1
            # The filter should be called with live_session__meeting_start_time__gte
            assert "live_session__meeting_start_time__gte" in str(filter_calls[0])

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_with_end_date(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test fetching participant class progress with end date filter."""
        # Arrange
        end_date = datetime(2025, 12, 31, tzinfo=timezone.utc)
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id, end_date=end_date
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify the query was built correctly with end date filter
            mocks["booking_filter"].assert_called_once_with(
                participant_id=mock_participant_id
            )
            # Verify filter was called for end date
            filter_calls = mocks["query"].filter.call_args_list
            assert len(filter_calls) == 1
            # The filter should be called with live_session__meeting_start_time__lt
            assert "live_session__meeting_start_time__lt" in str(filter_calls[0])

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_with_both_date_filters(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test fetching participant class progress with both start and end date filters."""
        # Arrange
        start_date = datetime(2025, 1, 1, tzinfo=timezone.utc)
        end_date = datetime(2025, 12, 31, tzinfo=timezone.utc)
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id,
                start_date=start_date,
                end_date=end_date,
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify the query was built correctly with both date filters
            mocks["booking_filter"].assert_called_once_with(
                participant_id=mock_participant_id
            )
            # Verify filter was called twice (once for start date, once for end date)
            filter_calls = mocks["query"].filter.call_args_list
            assert len(filter_calls) == 2

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_empty_result(
        self, mock_participant_id
    ):
        """Test fetching participant class progress when no bookings are found."""
        # Arrange
        mock_bookings = []

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 0

            # Verify the query was still built correctly
            mocks["booking_filter"].assert_called_once_with(
                participant_id=mock_participant_id
            )
            mocks["query"].prefetch_related.assert_called_once_with(
                "live_session__webinar__host", "participant"
            )
            mocks["query"].all.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_multiple_bookings(
        self, mock_participant_id, mock_raw_models
    ):
        """Test fetching participant class progress with multiple bookings."""
        # Arrange
        mock_booking1 = MagicMock()
        mock_booking1.id = uuid.uuid4()
        mock_booking1.live_session = MagicMock()
        mock_booking1.live_session.webinar = MagicMock()
        mock_booking1.live_session.webinar.host = MagicMock()
        mock_booking1.participant = MagicMock()

        mock_booking2 = MagicMock()
        mock_booking2.id = uuid.uuid4()
        mock_booking2.live_session = MagicMock()
        mock_booking2.live_session.webinar = MagicMock()
        mock_booking2.live_session.webinar.host = MagicMock()
        mock_booking2.participant = MagicMock()

        mock_bookings = [mock_booking1, mock_booking2]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 2

            # Verify all bookings were processed
            for item in result:
                assert isinstance(item, ParticipantClassProgress)

            # Verify model validation was called for each booking
            assert mocks["raw_live_session"].call_count == 2
            assert mocks["raw_participant"].call_count == 2
            assert mocks["raw_booking"].call_count == 2
            assert mocks["raw_authorized"].call_count == 2
            assert mocks["raw_webinar"].call_count == 2

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_date_edge_cases(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test fetching participant class progress with edge case dates."""
        # Arrange
        # Test with same start and end date
        same_date = datetime(2025, 6, 15, tzinfo=timezone.utc)
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id,
                start_date=same_date,
                end_date=same_date,
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify both date filters were applied
            filter_calls = mocks["query"].filter.call_args_list
            assert len(filter_calls) == 2

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_none_date_filters(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test fetching participant class progress with None date filters."""
        # Arrange
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id, start_date=None, end_date=None
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify no date filters were applied
            filter_calls = mocks["query"].filter.call_args_list
            assert len(filter_calls) == 0

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_ordering(
        self, mock_participant_id, mock_raw_models
    ):
        """Test that results are ordered by updated_at descending."""
        # Arrange
        mock_bookings = [MagicMock(), MagicMock()]
        for booking in mock_bookings:
            booking.live_session = MagicMock()
            booking.participant = MagicMock()

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 2

            # Verify order_by was called with "-updated_at"
            mocks["query"].all.return_value.order_by.assert_called_once_with(
                "-live_session__meeting_start_time"
            )

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_prefetch_relationships(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test that the correct relationships are prefetched."""
        # Arrange
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify the correct relationships were prefetched
            mocks["query"].prefetch_related.assert_called_once_with(
                "live_session__webinar__host", "participant"
            )

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_participant_class_progress_structure(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test that ParticipantClassProgress objects have the correct structure."""
        # Arrange
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            progress_item = result[0]
            assert isinstance(progress_item, ParticipantClassProgress)

            # Verify the ParticipantClassProgress was created with the correct arguments
            # The actual structure depends on how the mocks are set up, but we can verify
            # that the model validation methods were called with the right objects
            mocks["raw_live_session"].assert_called_once_with(mock_booking.live_session)
            mocks["raw_participant"].assert_called_once_with(mock_booking.participant)
            mocks["raw_booking"].assert_called_once_with(mock_booking)
            mocks["raw_authorized"].assert_called_once_with(
                mock_booking.live_session.webinar.host
            )
            mocks["raw_webinar"].assert_called_once_with(
                mock_booking.live_session.webinar
            )

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_with_future_dates(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test fetching participant class progress with future dates."""
        # Arrange
        future_start = datetime(2030, 1, 1, tzinfo=timezone.utc)
        future_end = datetime(2030, 12, 31, tzinfo=timezone.utc)
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id,
                start_date=future_start,
                end_date=future_end,
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify both date filters were applied
            filter_calls = mocks["query"].filter.call_args_list
            assert len(filter_calls) == 2

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_with_past_dates(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test fetching participant class progress with past dates."""
        # Arrange
        past_start = datetime(2020, 1, 1, tzinfo=timezone.utc)
        past_end = datetime(2020, 12, 31, tzinfo=timezone.utc)
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id,
                start_date=past_start,
                end_date=past_end,
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify both date filters were applied
            filter_calls = mocks["query"].filter.call_args_list
            assert len(filter_calls) == 2

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_invalid_participant_id(
        self, mock_raw_models
    ):
        """Test fetching participant class progress with different participant ID formats."""
        # Arrange
        invalid_participant_id = uuid.uuid4()  # Valid UUID but non-existent participant
        mock_bookings = []  # No bookings for this participant

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=invalid_participant_id
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 0

            # Verify the query was still executed with the provided participant ID
            mocks["booking_filter"].assert_called_once_with(
                participant_id=invalid_participant_id
            )

    @pytest.mark.parametrize(
        "start_date,end_date,expected_filter_calls",
        [
            (None, None, 0),  # No date filters
            (datetime(2025, 1, 1, tzinfo=timezone.utc), None, 1),  # Only start date
            (None, datetime(2025, 12, 31, tzinfo=timezone.utc), 1),  # Only end date
            (
                datetime(2025, 1, 1, tzinfo=timezone.utc),
                datetime(2025, 12, 31, tzinfo=timezone.utc),
                2,
            ),  # Both dates
        ],
    )
    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_date_filter_combinations(
        self,
        mock_participant_id,
        mock_booking,
        mock_raw_models,
        start_date,
        end_date,
        expected_filter_calls,
    ):
        """Test various combinations of date filters."""
        # Arrange
        mock_bookings = [mock_booking]

        async with setup_get_participant_classes_progress_mocks(
            mock_bookings=mock_bookings, mock_query_result=mock_raw_models
        ) as mocks:
            # Act
            result = await ParticipantRepository.get_participant_classes_progress(
                participant_id=mock_participant_id,
                start_date=start_date,
                end_date=end_date,
            )

            # Assert
            assert isinstance(result, list)
            assert len(result) == 1

            # Verify the correct number of filter calls were made
            filter_calls = mocks["query"].filter.call_args_list
            assert len(filter_calls) == expected_filter_calls

    @pytest.mark.asyncio
    async def test_get_participant_classes_progress_consistent_behavior(
        self, mock_participant_id, mock_booking, mock_raw_models
    ):
        """Test that the function behaves consistently across multiple calls."""
        # Arrange
        mock_bookings = [mock_booking]

        # Act & Assert - Call the function multiple times
        for _ in range(3):
            async with setup_get_participant_classes_progress_mocks(
                mock_bookings=mock_bookings, mock_query_result=mock_raw_models
            ) as mocks:
                result = await ParticipantRepository.get_participant_classes_progress(
                    participant_id=mock_participant_id
                )

                # Each call should produce the same result structure
                assert isinstance(result, list)
                assert len(result) == 1
                assert isinstance(result[0], ParticipantClassProgress)

                # Verify the query was built consistently
                mocks["booking_filter"].assert_called_once_with(
                    participant_id=mock_participant_id
                )
